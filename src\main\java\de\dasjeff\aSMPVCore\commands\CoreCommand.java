package de.dasjeff.aSMPVCore.commands;

import com.velocitypowered.api.command.CommandSource;
import de.dasjeff.aSMPVCore.ASMPVCore;
import de.dasjeff.aSMPVCore.managers.ModuleManager;
import de.dasjeff.aSMPVCore.util.MessageUtil;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;

import java.util.List;
import java.util.Map;

/**
 * Core command for ASMP-VCore plugin administration.
 * Provides status information, module management, and system diagnostics.
 */
public class CoreCommand extends AbstractCommand {

    public CoreCommand(ASMPVCore corePlugin) {
        super(corePlugin, "vcore", "asmpvcore.admin", "Main administration command for ASMP-VCore");
    }

    @Override
    protected void executeCommand(CommandSource source, String[] args) {
        if (args.length == 0) {
            sendHelp(source);
            return;
        }

        String subCommand = args[0].toLowerCase();
        switch (subCommand) {
            case "status" -> handleStatus(source);
            case "reload" -> handleReload(source, args);
            case "modules" -> handleModules(source);
            case "cache" -> handleCache(source);
            case "database" -> handleDatabase(source);
            case "security" -> handleSecurity(source);
            case "help" -> sendHelp(source);
            default -> {
                sendMessage(source, MessageUtil.prefixedError("Unknown subcommand: " + subCommand));
                sendHelp(source);
            }
        }
    }

    @Override
    protected List<String> getTabCompletions(CommandSource source, String[] args) {
        if (args.length == 1) {
            return List.of("status", "reload", "modules", "cache", "database", "security", "help");
        } else if (args.length == 2 && args[0].equalsIgnoreCase("reload")) {
            return List.of("config", "modules", "all");
        }
        return List.of();
    }

    private void handleStatus(CommandSource source) {
        sendMessage(source, MessageUtil.separator());
        sendMessage(source, Component.text("ASMP-VCore Status", NamedTextColor.BLUE));
        sendMessage(source, MessageUtil.separator());
        
        // Basic information
        sendMessage(source, Component.text("Version: ", NamedTextColor.GRAY)
                .append(Component.text(de.dasjeff.aSMPVCore.BuildConstants.VERSION, NamedTextColor.WHITE)));
        
        sendMessage(source, Component.text("Velocity Version: ", NamedTextColor.GRAY)
                .append(Component.text(corePlugin.getProxyServer().getVersion().getVersion(), NamedTextColor.WHITE)));
        
        // Manager status
        sendMessage(source, Component.text("Database: ", NamedTextColor.GRAY)
                .append(Component.text(corePlugin.getDatabaseManager() != null && corePlugin.getDatabaseManager().isConnected() ? "Connected" : "Disconnected", 
                        corePlugin.getDatabaseManager() != null && corePlugin.getDatabaseManager().isConnected() ? NamedTextColor.GREEN : NamedTextColor.RED)));
        
        sendMessage(source, Component.text("Redis: ", NamedTextColor.GRAY)
                .append(Component.text(corePlugin.getCacheManager() != null && corePlugin.getCacheManager().isRedisAvailable() ? "Connected" : "Disconnected",
                        corePlugin.getCacheManager() != null && corePlugin.getCacheManager().isRedisAvailable() ? NamedTextColor.GREEN : NamedTextColor.RED)));
        
        // Module information
        if (corePlugin.getModuleManager() != null) {
            Map<ModuleManager.ModuleState, Integer> moduleCounts = corePlugin.getModuleManager().getModuleStateCounts();
            sendMessage(source, Component.text("Modules: ", NamedTextColor.GRAY)
                    .append(Component.text(moduleCounts.get(ModuleManager.ModuleState.ENABLED) + " enabled, " +
                            moduleCounts.get(ModuleManager.ModuleState.ERROR) + " errors", NamedTextColor.WHITE)));
        }
        
        sendMessage(source, MessageUtil.separator());
    }

    private void handleReload(CommandSource source, String[] args) {
        if (args.length < 2) {
            sendMessage(source, MessageUtil.prefixedError("Usage: /vcore reload <config|modules|all>"));
            return;
        }

        String reloadType = args[1].toLowerCase();
        switch (reloadType) {
            case "config" -> {
                executeAsync(() -> {
                    corePlugin.getConfigManager().reloadMainConfig();
                    sendMessage(source, MessageUtil.prefixedSuccess("Configuration reloaded successfully!"));
                });
            }
            case "modules" -> {
                executeAsync(() -> {
                    int reloaded = corePlugin.getModuleManager().reloadAllModules();
                    sendMessage(source, MessageUtil.prefixedSuccess("Reloaded " + reloaded + " modules successfully!"));
                });
            }
            case "all" -> {
                executeAsync(() -> {
                    corePlugin.getConfigManager().reloadMainConfig();
                    int reloaded = corePlugin.getModuleManager().reloadAllModules();
                    sendMessage(source, MessageUtil.prefixedSuccess("Reloaded configuration and " + reloaded + " modules successfully!"));
                });
            }
            default -> sendMessage(source, MessageUtil.prefixedError("Invalid reload type. Use: config, modules, or all"));
        }
    }

    private void handleModules(CommandSource source) {
        if (corePlugin.getModuleManager() == null) {
            sendMessage(source, MessageUtil.prefixedError("Module manager is not available."));
            return;
        }

        String report = corePlugin.getModuleManager().getModuleStatusReport();
        for (String line : report.split("\n")) {
            sendMessage(source, Component.text(line, NamedTextColor.WHITE));
        }
    }

    private void handleCache(CommandSource source) {
        if (corePlugin.getCacheManager() == null) {
            sendMessage(source, MessageUtil.prefixedError("Cache manager is not available."));
            return;
        }

        sendMessage(source, MessageUtil.separator());
        sendMessage(source, Component.text("Cache Status", NamedTextColor.BLUE));
        sendMessage(source, MessageUtil.separator());
        
        sendMessage(source, Component.text("Redis Available: ", NamedTextColor.GRAY)
                .append(Component.text(corePlugin.getCacheManager().isRedisAvailable() ? "Yes" : "No",
                        corePlugin.getCacheManager().isRedisAvailable() ? NamedTextColor.GREEN : NamedTextColor.RED)));
        
        sendMessage(source, MessageUtil.separator());
    }

    private void handleDatabase(CommandSource source) {
        if (corePlugin.getDatabaseManager() == null) {
            sendMessage(source, MessageUtil.prefixedError("Database manager is not available."));
            return;
        }

        sendMessage(source, MessageUtil.separator());
        sendMessage(source, Component.text("Database Status", NamedTextColor.BLUE));
        sendMessage(source, MessageUtil.separator());
        
        sendMessage(source, Component.text("Connected: ", NamedTextColor.GRAY)
                .append(Component.text(corePlugin.getDatabaseManager().isConnected() ? "Yes" : "No",
                        corePlugin.getDatabaseManager().isConnected() ? NamedTextColor.GREEN : NamedTextColor.RED)));
        
        if (corePlugin.getDatabaseManager().isConnected()) {
            String poolStats = corePlugin.getDatabaseManager().getPoolStats();
            sendMessage(source, Component.text("Pool Stats: ", NamedTextColor.GRAY)
                    .append(Component.text(poolStats, NamedTextColor.WHITE)));
        }
        
        sendMessage(source, MessageUtil.separator());
    }

    private void handleSecurity(CommandSource source) {
        if (corePlugin.getSecurityManager() == null) {
            sendMessage(source, MessageUtil.prefixedError("Security manager is not available."));
            return;
        }

        sendMessage(source, MessageUtil.separator());
        sendMessage(source, Component.text("Security Status", NamedTextColor.BLUE));
        sendMessage(source, MessageUtil.separator());
        
        String securityStats = corePlugin.getSecurityManager().getSecurityStats();
        sendMessage(source, Component.text(securityStats, NamedTextColor.WHITE));
        
        sendMessage(source, MessageUtil.separator());
    }

    private void sendHelp(CommandSource source) {
        sendMessage(source, MessageUtil.separator());
        sendMessage(source, Component.text("ASMP-VCore Commands", NamedTextColor.BLUE));
        sendMessage(source, MessageUtil.separator());
        
        sendMessage(source, Component.text("/vcore status", NamedTextColor.YELLOW)
                .append(Component.text(" - Show plugin status", NamedTextColor.GRAY)));
        
        sendMessage(source, Component.text("/vcore reload <config|modules|all>", NamedTextColor.YELLOW)
                .append(Component.text(" - Reload configuration or modules", NamedTextColor.GRAY)));
        
        sendMessage(source, Component.text("/vcore modules", NamedTextColor.YELLOW)
                .append(Component.text(" - Show module status", NamedTextColor.GRAY)));
        
        sendMessage(source, Component.text("/vcore cache", NamedTextColor.YELLOW)
                .append(Component.text(" - Show cache status", NamedTextColor.GRAY)));
        
        sendMessage(source, Component.text("/vcore database", NamedTextColor.YELLOW)
                .append(Component.text(" - Show database status", NamedTextColor.GRAY)));
        
        sendMessage(source, Component.text("/vcore security", NamedTextColor.YELLOW)
                .append(Component.text(" - Show security statistics", NamedTextColor.GRAY)));
        
        sendMessage(source, MessageUtil.separator());
    }

    @Override
    protected String getUsage() {
        return "/vcore <status|reload|modules|cache|database|security|help>";
    }
}
