package de.dasjeff.adventureSMPCore.modules.homesystem.commands;

import de.dasjeff.adventureSMPCore.AdventureSMPCore;
import de.dasjeff.adventureSMPCore.modules.homesystem.HomeModule;
import de.dasjeff.adventureSMPCore.modules.homesystem.validation.ValidationResult;
import de.dasjeff.adventureSMPCore.util.PermissionUtil;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

import java.util.Collections;
import java.util.List;

public class SetHomeCommand extends BaseHomeCommand {

    public SetHomeCommand(AdventureSMPCore corePlugin, HomeModule homeModule) {
        super(corePlugin, homeModule,
                "sethome",
                PermissionUtil.getFullPermission("homes.sethome"),
                true,
                "Sets a home at your current location.",
                "/sethome <HomeName>",
                Collections.singletonList("createhome"));
    }

    @Override
    protected boolean executeCommand(CommandSender sender, String[] args) {
        Player player = (Player) sender;

        if (args.length == 0) {
            sendMessage(player, "command_usage", "{command_usage}", getUsageMessage());
            return true;
        }

        String homeName = args[0];

        // Set home immediately and provide instant feedback
        ValidationResult result = homeService.setHomeImmediate(player, homeName);
        
        if (handleValidationResult(player, result)) {
            sendMessage(player, "home_set_success", "{home_name}", homeName);
            playSound(player, homeModule.getHomeConfig().getSound("set_home"));
        }

        return true;
    }

    @Override
    public List<String> tabComplete(CommandSender sender, String[] args) {
        // No tab completion for home names when setting
        return Collections.emptyList();
    }
} 