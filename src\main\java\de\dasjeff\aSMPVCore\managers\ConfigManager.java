package de.dasjeff.aSMPVCore.managers;

import de.dasjeff.aSMPVCore.ASMPVCore;
import org.yaml.snakeyaml.DumperOptions;
import org.yaml.snakeyaml.Yaml;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.HashMap;
import java.util.Map;

/**
 * Manages configuration files for the ASMP-VCore plugin.
 * Handles loading, saving, and accessing configuration values.
 */
public class ConfigManager {

    private final ASMPVCore corePlugin;
    private final Path configDirectory;
    private final Yaml yaml;
    private Map<String, Object> mainConfig;

    public ConfigManager(ASMPVCore corePlugin) {
        this.corePlugin = corePlugin;
        this.configDirectory = corePlugin.getDataDirectory();
        
        // Configure YAML with proper formatting
        DumperOptions options = new DumperOptions();
        options.setDefaultFlowStyle(DumperOptions.FlowStyle.BLOCK);
        options.setPrettyFlow(true);
        options.setIndent(2);
        this.yaml = new Yaml(options);
        
        setupMainConfig();
    }

    private void setupMainConfig() {
        try {
            // Create data directory if it doesn't exist
            if (!Files.exists(configDirectory)) {
                Files.createDirectories(configDirectory);
            }

            Path mainConfigFile = configDirectory.resolve("config.yml");
            
            if (!Files.exists(mainConfigFile)) {
                // Create default config
                createDefaultMainConfig(mainConfigFile);
            }
            
            // Load config
            loadMainConfig(mainConfigFile);
            
        } catch (IOException e) {
            corePlugin.getLogger().error("Failed to setup main configuration!", e);
            // Use empty config as fallback
            mainConfig = new HashMap<>();
        }
    }

    private void createDefaultMainConfig(Path configFile) throws IOException {
        Map<String, Object> defaultConfig = new HashMap<>();
        
        // Database configuration
        Map<String, Object> database = new HashMap<>();
        database.put("enabled", true);
        database.put("host", "localhost");
        database.put("port", 3306);
        database.put("database", "asmp_vcore");
        database.put("username", "root");
        database.put("password", "your_password");
        database.put("pool_size", 10);
        database.put("connection_timeout", 30000);
        database.put("idle_timeout", 600000);
        database.put("max_lifetime", 1800000);
        defaultConfig.put("database", database);

        // Redis configuration
        Map<String, Object> redis = new HashMap<>();
        redis.put("enabled", true);
        redis.put("host", "localhost");
        redis.put("port", 6379);
        redis.put("password", "");
        redis.put("database", 0);
        redis.put("timeout", 2000);
        redis.put("pool_max_total", 20);
        redis.put("pool_max_idle", 10);
        redis.put("pool_min_idle", 2);
        defaultConfig.put("redis", redis);

        // Cache configuration
        Map<String, Object> cache = new HashMap<>();
        cache.put("enable_statistics", true);
        cache.put("default_max_size", 1000);
        cache.put("default_expire_minutes", 30);
        
        Map<String, Object> caches = new HashMap<>();
        Map<String, Object> playerDataCache = new HashMap<>();
        playerDataCache.put("max_size", 500);
        playerDataCache.put("expire_minutes", 10);
        caches.put("playerDataCache", playerDataCache);
        cache.put("caches", caches);
        defaultConfig.put("cache", cache);

        // Security configuration
        Map<String, Object> security = new HashMap<>();
        security.put("command_cooldown_ms", 100);
        security.put("max_async_operations_per_player", 10);
        security.put("rate_limit_enabled", true);
        security.put("rate_limit_requests_per_minute", 60);
        defaultConfig.put("security", security);

        // Webpanel configuration (for future use)
        Map<String, Object> webpanel = new HashMap<>();
        webpanel.put("enabled", false);
        webpanel.put("api_key", "change_this_api_key");
        webpanel.put("allowed_ips", new String[]{"127.0.0.1"});
        defaultConfig.put("webpanel", webpanel);

        // Debug configuration
        Map<String, Object> debug = new HashMap<>();
        debug.put("enabled", false);
        debug.put("log_sql_queries", false);
        debug.put("log_cache_operations", false);
        defaultConfig.put("debug", debug);

        // Write to file
        try (FileWriter writer = new FileWriter(configFile.toFile())) {
            yaml.dump(defaultConfig, writer);
        }
        
        corePlugin.getLogger().info("Created default configuration file: {}", configFile);
    }

    @SuppressWarnings("unchecked")
    private void loadMainConfig(Path configFile) throws IOException {
        try (FileReader reader = new FileReader(configFile.toFile())) {
            Object loaded = yaml.load(reader);
            if (loaded instanceof Map) {
                mainConfig = (Map<String, Object>) loaded;
            } else {
                mainConfig = new HashMap<>();
            }
        }
    }

    /**
     * Gets the main configuration map.
     * @return The main configuration.
     */
    public ConfigWrapper getMainConfig() {
        return new ConfigWrapper(mainConfig);
    }

    /**
     * Saves the main configuration to file.
     */
    public void saveMainConfig() {
        try {
            Path mainConfigFile = configDirectory.resolve("config.yml");
            try (FileWriter writer = new FileWriter(mainConfigFile.toFile())) {
                yaml.dump(mainConfig, writer);
            }
        } catch (IOException e) {
            corePlugin.getLogger().error("Failed to save main configuration!", e);
        }
    }

    /**
     * Reloads the main configuration from file.
     */
    public void reloadMainConfig() {
        try {
            Path mainConfigFile = configDirectory.resolve("config.yml");
            loadMainConfig(mainConfigFile);
        } catch (IOException e) {
            corePlugin.getLogger().error("Failed to reload main configuration!", e);
        }
    }

    /**
     * Loads a custom configuration file for a module.
     * @param moduleName The name of the module (used for subdirectory).
     * @param fileName The name of the configuration file.
     * @return A ConfigWrapper for the loaded configuration, or null if loading failed.
     */
    @SuppressWarnings("unchecked")
    public ConfigWrapper loadCustomConfig(String moduleName, String fileName) {
        try {
            Path moduleDir = configDirectory.resolve(moduleName);
            if (!Files.exists(moduleDir)) {
                Files.createDirectories(moduleDir);
            }

            Path configFile = moduleDir.resolve(fileName);
            if (!Files.exists(configFile)) {
                // Create empty config file
                try (FileWriter writer = new FileWriter(configFile.toFile())) {
                    yaml.dump(new HashMap<>(), writer);
                }
            }

            try (FileReader reader = new FileReader(configFile.toFile())) {
                Object loaded = yaml.load(reader);
                if (loaded instanceof Map) {
                    return new ConfigWrapper((Map<String, Object>) loaded);
                } else {
                    return new ConfigWrapper(new HashMap<>());
                }
            }
        } catch (IOException e) {
            corePlugin.getLogger().error("Failed to load custom config {}/{}", moduleName, fileName, e);
            return null;
        }
    }

    /**
     * Saves a custom configuration file for a module.
     * @param moduleName The name of the module.
     * @param fileName The name of the configuration file.
     * @param config The configuration to save.
     */
    public void saveCustomConfig(String moduleName, String fileName, Map<String, Object> config) {
        try {
            Path moduleDir = configDirectory.resolve(moduleName);
            if (!Files.exists(moduleDir)) {
                Files.createDirectories(moduleDir);
            }

            Path configFile = moduleDir.resolve(fileName);
            try (FileWriter writer = new FileWriter(configFile.toFile())) {
                yaml.dump(config, writer);
            }
        } catch (IOException e) {
            corePlugin.getLogger().error("Failed to save custom config {}/{}", moduleName, fileName, e);
        }
    }

    /**
     * Wrapper class for configuration maps to provide type-safe access methods.
     */
    public static class ConfigWrapper {
        private final Map<String, Object> config;

        public ConfigWrapper(Map<String, Object> config) {
            this.config = config != null ? config : new HashMap<>();
        }

        public String getString(String path, String defaultValue) {
            Object value = getNestedValue(path);
            return value instanceof String ? (String) value : defaultValue;
        }

        public int getInt(String path, int defaultValue) {
            Object value = getNestedValue(path);
            return value instanceof Number ? ((Number) value).intValue() : defaultValue;
        }

        public long getLong(String path, long defaultValue) {
            Object value = getNestedValue(path);
            return value instanceof Number ? ((Number) value).longValue() : defaultValue;
        }

        public boolean getBoolean(String path, boolean defaultValue) {
            Object value = getNestedValue(path);
            return value instanceof Boolean ? (Boolean) value : defaultValue;
        }

        public double getDouble(String path, double defaultValue) {
            Object value = getNestedValue(path);
            return value instanceof Number ? ((Number) value).doubleValue() : defaultValue;
        }

        @SuppressWarnings("unchecked")
        private Object getNestedValue(String path) {
            String[] keys = path.split("\\.");
            Object current = config;
            
            for (String key : keys) {
                if (current instanceof Map) {
                    current = ((Map<String, Object>) current).get(key);
                } else {
                    return null;
                }
            }
            
            return current;
        }

        public Map<String, Object> getMap() {
            return config;
        }
    }
}
