plugins {
    id 'java'
    id 'eclipse'
    id 'org.jetbrains.gradle.plugin.idea-ext' version '1.1.8'
    id("xyz.jpenilla.run-velocity") version "2.3.1"
}

group = 'de.dasjeff'
version = '1.0'

repositories {
    mavenCentral()
    maven {
        name = "papermc-repo"
        url = "https://repo.papermc.io/repository/maven-public/"
    }
    maven {
        name = "sonatype"
        url = "https://oss.sonatype.org/content/groups/public/"
    }
}

dependencies {
    compileOnly("com.velocitypowered:velocity-api:3.4.0-SNAPSHOT")
    annotationProcessor("com.velocitypowered:velocity-api:3.4.0-SNAPSHOT")
}

tasks {
    runVelocity {
        // Configure the Velocity version for our task.
        // This is the only required configuration besides applying the plugin.
        // Your plugin's jar (or shadowJar if present) will be used automatically.
        velocityVersion("3.4.0-SNAPSHOT")
    }
}

def targetJavaVersion = 21
java {
    toolchain.languageVersion = JavaLanguageVersion.of(targetJavaVersion)
}

tasks.withType(JavaCompile).configureEach {
    options.encoding = 'UTF-8'
    options.release.set(targetJavaVersion)
}

def templateSource = file('src/main/templates')
def templateDest = layout.buildDirectory.dir('generated/sources/templates')
def generateTemplates = tasks.register('generateTemplates', Copy) { task ->
    def props = ['version': project.version]
    task.inputs.properties props

    task.from templateSource
    task.into templateDest
    task.expand props
}

sourceSets.main.java.srcDir(generateTemplates.map { it.outputs })

project.idea.project.settings.taskTriggers.afterSync generateTemplates
project.eclipse.synchronizationTasks(generateTemplates)
