package de.dasjeff.adventureSMPCore.modules.homesystem.commands;

import de.dasjeff.adventureSMPCore.AdventureSMPCore;
import de.dasjeff.adventureSMPCore.modules.homesystem.HomeModule;
import de.dasjeff.adventureSMPCore.util.PermissionUtil;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

import java.util.Collections;
import java.util.List;

public class DelHomeCommand extends BaseHomeCommand {

    public DelHomeCommand(AdventureSMPCore corePlugin, HomeModule homeModule) {
        super(corePlugin, homeModule,
                "delhome",
                PermissionUtil.getFullPermission("homes.delhome"),
                true,
                "Deletes one of your homes.",
                "/delhome <HomeName>",
                Collections.singletonList("removehome"));
    }

    @Override
    protected boolean executeCommand(CommandSender sender, String[] args) {
        Player player = (Player) sender;

        if (args.length == 0) {
            sendMessage(player, "command_usage", "{command_usage}", getUsageMessage());
            return true;
        }

        String homeName = args[0];

        // Delete home immediately and provide instant feedback
        boolean success = homeService.deleteHomeImmediate(player.getUniqueId(), homeName);
        
        if (success) {
            sendMessage(player, "home_delete_success", "{home_name}", homeName);
            playSound(player, homeModule.getHomeConfig().getSound("delete_home"));
        } else {
            sendMessage(player, "home_delete_not_found", "{home_name}", homeName);
            playSound(player, homeModule.getHomeConfig().getSound("error"));
        }

        return true;
    }

    @Override
    public List<String> tabComplete(CommandSender sender, String[] args) {
        if (!(sender instanceof Player player) || args.length > 1) {
            return Collections.emptyList();
        }

        String currentArg = args.length == 1 ? args[0] : "";
        return getHomeNameCompletions(player, currentArg);
    }
} 