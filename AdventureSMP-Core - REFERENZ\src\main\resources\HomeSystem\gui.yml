# ===================================================================
#    Konfiguration der GUI für das AdventureSMP HomeSystem
# ===================================================================


# ===================================================================
#                 HOME-LISTE (HAUPT-GUI - /homes)
# ===================================================================
home_list:
  # Titel der Haupt-GUI, die alle Home-Punkte auflistet.
  # Platzhalter: {current_page}, {total_pages}, {player_name}
  title: "&1Deine Home-Punkte (Seite {current_page}/{total_pages})"

  # Anzahl der Zeilen (gültige Werte: 1-6).
  # Eine zusätzliche Zeile für die Seitennavigation wird automatisch hinzugefügt.
  rows: 3


  # --- Item für einen Home-Punkt ---
  item:
    # Item, das ein einzelnes Home darstellt.
    material: "PAPER"
    # Name des Home-Items. Platzhalter: {home_name}
    name: "&b{home_name}"
    # Beschreibung des Home-Items. Platzhalter: {world_name}, {x}, {y}, {z}
    lore:
      - "&7Welt: &f{world_name}"
      - "&7Koordinaten: &fX:{x} Y:{y} Z:{z}"
      - " "
      - "&eLinksklick zum Teleportieren."
      - "&cRechtsklick zum Löschen."
    # Glow für das Item.
    glow: false
    # ID für ein Resource-Pack-Modell (falls vorhanden, sonst 0).
    custom_model_data: 0


  # --- Seitennavigation (Vor/Zurück-Buttons) ---
  next_page_item:
    # Item, das die Navigation nach rechts darstellt.
    material: "ARROW"
    # Name des Nächste-Seite-Items.
    name: "&aNächste Seite ->"
    # Glow für das Item.
    glow: false
    # ID für ein Resource-Pack-Modell (falls vorhanden, sonst 0).
    custom_model_data: 0
    # Slot-Position (0-8) in der Navigationszeile. Standard: 8
    slot: 8

  prev_page_item:
    # Item, das die Navigation nach links darstellt.
    material: "ARROW"
    # Name des Vorherige-Seite-Items.
    name: "&a<- Vorherige Seite"
    # Glow für das Item.
    glow: false
    # ID für ein Resource-Pack-Modell (falls vorhanden, sonst 0).
    custom_model_data: 0
    # Slot-Position (0-8) in der Navigationszeile. Standard: 0
    slot: 0


  # --- Platzhalter-Items für leere Slots ---
  fill_empty_slots:
    # Aktiviert/Deaktiviert das Auffüllen.
    enabled: true
    # Item, das für leere Slots verwendet wird.
    material: "GRAY_STAINED_GLASS_PANE"
    # Name für das Füll-Item.
    name: " "
    # Glow für das Item.
    glow: false
    # ID für ein Resource-Pack-Modell (falls vorhanden, sonst 0).
    custom_model_data: 0


# ===================================================================
#                 LÖSCHEN BESTÄTIGEN GUI
# ===================================================================
delete_confirmation:
  # Titel des Lösch-Bestätigungs-GUI's. Platzhalter: {home_name}
  title: "&4Löschen bestätigen: {home_name}"

  # --- Item zum BESTÄTIGEN des Löschens ---
  confirm_item:
    # Item, dass das Löschen bestätigt.
    material: "RED_WOOL"
    # Name des Bestätigungs-Items. Platzhalter: {home_name}
    name: "&cLÖSCHEN VON '{home_name}' BESTÄTIGEN"
    # Beschreibung des Bestätigungs-Items.
    lore:
      - "&7Diese Aktion kann nicht rückgängig gemacht werden."
    # Glow für das Item.
    glow: false
    # ID für ein Resource-Pack-Modell (falls vorhanden, sonst 0).
    custom_model_data: 0
    # Slot-Position von 0-8.
    slot: 2


  # --- Item zum ABBRECHEN des Löschens ---
  cancel_item:
    material: "GREEN_WOOL"
    # Name des Abbrechen-Items.
    name: "&aABBRECHEN"
    # Glow für das Item.
    glow: false
    # ID für ein Resource-Pack-Modell (falls vorhanden, sonst 0).
    custom_model_data: 0
    # Slot-Position von 0-8.
    slot: 6

  # --- Platzhalter-Items für Bestätigungs-GUI ---
  fill_empty_slots:
    # Aktiviert/Deaktiviert das Auffüllen.
    enabled: true
    # Material für die Füll-Items in dieser GUI.
    material: "BLACK_STAINED_GLASS_PANE"
    # Name für das Füll-Item.
    name: " "
    # Glow für das Item.
    glow: false
    # ID für ein Resource-Pack-Modell (falls vorhanden, sonst 0).
    custom_model_data: 0
