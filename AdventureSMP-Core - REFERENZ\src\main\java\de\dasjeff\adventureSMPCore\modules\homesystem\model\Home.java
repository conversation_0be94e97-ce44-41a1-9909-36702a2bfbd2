package de.dasjeff.adventureSMPCore.modules.homesystem.model;

import org.bukkit.Location;
import org.bukkit.World;
import org.jetbrains.annotations.NotNull;

import java.util.UUID;

public class Home {
    private int id;
    private final UUID playerUuid;
    private final String homeName;
    private final UUID worldUuid;
    private final double x, y, z;
    private final float pitch, yaw;

    public Home(@NotNull UUID playerUuid, @NotNull String homeName, @NotNull Location location) {
        this(-1, playerUuid, homeName, location.getWorld().getUID(), 
             location.getX(), location.getY(), location.getZ(), 
             location.getPitch(), location.getYaw());
    }

    public Home(int id, @NotNull UUID playerUuid, @NotNull String homeName, @NotNull UUID worldUuid, 
                double x, double y, double z, float pitch, float yaw) {
        this.id = id;
        this.playerUuid = playerUuid;
        this.homeName = homeName;
        this.worldUuid = worldUuid;
        this.x = x;
        this.y = y;
        this.z = z;
        this.pitch = pitch;
        this.yaw = yaw;
    }

    // Getters
    public int getId() {
        return id;
    }

    public void setId(int id) { // Setter might be useful if ID is generated by <PERSON> and needs to be updated post-insert
        this.id = id;
    }

    @NotNull
    public UUID getPlayerUuid() {
        return playerUuid;
    }

    @NotNull
    public String getHomeName() {
        return homeName;
    }

    @NotNull
    public UUID getWorldUuid() {
        return worldUuid;
    }

    public double getX() {
        return x;
    }

    public double getY() {
        return y;
    }

    public double getZ() {
        return z;
    }

    public float getPitch() {
        return pitch;
    }

    public float getYaw() {
        return yaw;
    }

    public Location toLocation(World world) {
        if (world == null || !world.getUID().equals(this.worldUuid)) {
            World correctWorld = org.bukkit.Bukkit.getWorld(this.worldUuid);
            if (correctWorld == null) return null;
            return new Location(correctWorld, x, y, z, yaw, pitch);
        }
        return new Location(world, x, y, z, yaw, pitch);
    }
    
    public Location toLocation() {
        World world = org.bukkit.Bukkit.getWorld(this.worldUuid);
        if (world == null) return null;
        return new Location(world, x, y, z, yaw, pitch);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Home home = (Home) o;
        return playerUuid.equals(home.playerUuid) && homeName.equalsIgnoreCase(home.homeName);
    }

    @Override
    public int hashCode() {
        return java.util.Objects.hash(playerUuid, homeName.toLowerCase());
    }
} 