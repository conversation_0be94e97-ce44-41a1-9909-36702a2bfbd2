package de.dasjeff.aSMPVCore.util;

import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import net.kyori.adventure.text.format.TextDecoration;
import net.kyori.adventure.text.minimessage.MiniMessage;
import net.kyori.adventure.text.serializer.legacy.LegacyComponentSerializer;

/**
 * Utility class for handling messages and text formatting.
 * Provides methods for formatting text with MiniMessage and legacy color codes.
 */
public final class MessageUtil {

    private static final MiniMessage MINI_MESSAGE = MiniMessage.miniMessage();
    private static final LegacyComponentSerializer LEGACY_SERIALIZER = LegacyComponentSerializer.legacyAmpersand();

    private MessageUtil() {
        // Utility class - no instantiation
    }

    /**
     * Formats a message using MiniMessage format.
     * @param message The message to format.
     * @return The formatted Component.
     */
    public static Component formatMiniMessage(String message) {
        if (message == null || message.isEmpty()) {
            return Component.empty();
        }
        
        try {
            return MINI_MESSAGE.deserialize(message);
        } catch (Exception e) {
            // Fallback to plain text if parsing fails
            return Component.text(message);
        }
    }

    /**
     * Formats a message using legacy color codes (&).
     * @param message The message to format.
     * @return The formatted Component.
     */
    public static Component formatLegacy(String message) {
        if (message == null || message.isEmpty()) {
            return Component.empty();
        }
        
        return LEGACY_SERIALIZER.deserialize(message);
    }

    /**
     * Applies placeholders to a message.
     * @param message The message template.
     * @param placeholders The placeholders in pairs (placeholder, value, placeholder, value, ...).
     * @return The message with placeholders replaced.
     */
    public static String applyPlaceholders(String message, String... placeholders) {
        if (message == null || placeholders.length == 0) {
            return message;
        }
        
        String result = message;
        for (int i = 0; i < placeholders.length - 1; i += 2) {
            String placeholder = placeholders[i];
            String value = placeholders[i + 1];
            
            if (placeholder != null && value != null) {
                result = result.replace(placeholder, value);
            }
        }
        
        return result;
    }

    /**
     * Creates a success message component.
     * @param message The message text.
     * @return A green success message component.
     */
    public static Component success(String message) {
        return Component.text(message, NamedTextColor.GREEN);
    }

    /**
     * Creates an error message component.
     * @param message The message text.
     * @return A red error message component.
     */
    public static Component error(String message) {
        return Component.text(message, NamedTextColor.RED);
    }

    /**
     * Creates a warning message component.
     * @param message The message text.
     * @return A yellow warning message component.
     */
    public static Component warning(String message) {
        return Component.text(message, NamedTextColor.YELLOW);
    }

    /**
     * Creates an info message component.
     * @param message The message text.
     * @return A blue info message component.
     */
    public static Component info(String message) {
        return Component.text(message, NamedTextColor.BLUE);
    }

    /**
     * Creates a prefix component for the plugin.
     * @return The plugin prefix component.
     */
    public static Component getPrefix() {
        return Component.text("[", NamedTextColor.GRAY)
                .append(Component.text("ASMP-VCore", NamedTextColor.BLUE, TextDecoration.BOLD))
                .append(Component.text("] ", NamedTextColor.GRAY));
    }

    /**
     * Creates a message with the plugin prefix.
     * @param message The message component.
     * @return The message with prefix.
     */
    public static Component withPrefix(Component message) {
        return getPrefix().append(message);
    }

    /**
     * Creates a message with the plugin prefix.
     * @param message The message text.
     * @return The message with prefix.
     */
    public static Component withPrefix(String message) {
        return withPrefix(Component.text(message));
    }

    /**
     * Creates a success message with prefix.
     * @param message The message text.
     * @return A success message with prefix.
     */
    public static Component prefixedSuccess(String message) {
        return withPrefix(success(message));
    }

    /**
     * Creates an error message with prefix.
     * @param message The message text.
     * @return An error message with prefix.
     */
    public static Component prefixedError(String message) {
        return withPrefix(error(message));
    }

    /**
     * Creates a warning message with prefix.
     * @param message The message text.
     * @return A warning message with prefix.
     */
    public static Component prefixedWarning(String message) {
        return withPrefix(warning(message));
    }

    /**
     * Creates an info message with prefix.
     * @param message The message text.
     * @return An info message with prefix.
     */
    public static Component prefixedInfo(String message) {
        return withPrefix(info(message));
    }

    /**
     * Converts a Component to a legacy string.
     * @param component The component to convert.
     * @return The legacy string representation.
     */
    public static String toLegacyString(Component component) {
        return LEGACY_SERIALIZER.serialize(component);
    }

    /**
     * Converts a Component to a MiniMessage string.
     * @param component The component to convert.
     * @return The MiniMessage string representation.
     */
    public static String toMiniMessageString(Component component) {
        return MINI_MESSAGE.serialize(component);
    }

    /**
     * Strips all formatting from a message.
     * @param message The message to strip.
     * @return The plain text without formatting.
     */
    public static String stripFormatting(String message) {
        if (message == null || message.isEmpty()) {
            return message;
        }
        
        // Remove legacy color codes
        String result = message.replaceAll("&[0-9a-fk-or]", "");
        
        // Remove MiniMessage tags (basic removal)
        result = result.replaceAll("<[^>]*>", "");
        
        return result;
    }

    /**
     * Truncates a message to a maximum length.
     * @param message The message to truncate.
     * @param maxLength The maximum length.
     * @return The truncated message.
     */
    public static String truncate(String message, int maxLength) {
        if (message == null || message.length() <= maxLength) {
            return message;
        }
        
        return message.substring(0, maxLength - 3) + "...";
    }

    /**
     * Centers text within a given width.
     * @param text The text to center.
     * @param width The total width.
     * @return The centered text.
     */
    public static String center(String text, int width) {
        if (text == null || text.length() >= width) {
            return text;
        }
        
        int padding = (width - text.length()) / 2;
        StringBuilder result = new StringBuilder();
        
        for (int i = 0; i < padding; i++) {
            result.append(" ");
        }
        
        result.append(text);
        
        while (result.length() < width) {
            result.append(" ");
        }
        
        return result.toString();
    }

    /**
     * Creates a separator line.
     * @param character The character to use for the separator.
     * @param length The length of the separator.
     * @param color The color of the separator.
     * @return The separator component.
     */
    public static Component separator(char character, int length, NamedTextColor color) {
        StringBuilder separator = new StringBuilder();
        for (int i = 0; i < length; i++) {
            separator.append(character);
        }
        return Component.text(separator.toString(), color);
    }

    /**
     * Creates a default separator line.
     * @return A gray separator line with dashes.
     */
    public static Component separator() {
        return separator('-', 50, NamedTextColor.GRAY);
    }
}
