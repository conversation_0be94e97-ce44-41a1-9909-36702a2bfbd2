package de.dasjeff.adventureSMPCore.modules.homesystem.validation;

import de.dasjeff.adventureSMPCore.AdventureSMPCore;
import de.dasjeff.adventureSMPCore.modules.homesystem.HomeModule;
import de.dasjeff.adventureSMPCore.modules.homesystem.config.HomeConfig;
import de.dasjeff.adventureSMPCore.modules.homesystem.database.HomeDataAccessor;
import de.dasjeff.adventureSMPCore.modules.homesystem.model.Home;
import de.dasjeff.adventureSMPCore.util.PermissionUtil;
import org.bukkit.World;
import org.bukkit.entity.Player;
import org.jetbrains.annotations.NotNull;

import java.util.List;
import java.util.regex.Pattern;

/**
 * Validator for home operations.
 */
public class HomeValidator {

    private final AdventureSMPCore corePlugin;
    private final HomeConfig homeConfig;
    private final HomeDataAccessor dataAccessor;

    private static final Pattern VALID_HOME_NAME_PATTERN = Pattern.compile("^[a-zA-Z0-9_-]+$");

    public HomeValidator(AdventureSMPCore corePlugin, HomeConfig homeConfig, HomeDataAccessor dataAccessor) {
        this.corePlugin = corePlugin;
        this.homeConfig = homeConfig;
        this.dataAccessor = dataAccessor;
    }

    /**
     * Gets minimum home name length from HomeConfig.
     */
    private int getMinHomeNameLength() {
        return homeConfig.getMinHomeNameLength();
    }

    /**
     * Gets maximum home name length from HomeConfig.
     */
    private int getMaxHomeNameLength() {
        return homeConfig.getMaxHomeNameLength();
    }

    /**
     * Validates a home name for format and length requirements.
     */
    public ValidationResult validateHomeName(@NotNull String homeName) {
        int minLength = getMinHomeNameLength();
        int maxLength = getMaxHomeNameLength();
        
        if (homeName.length() < minLength) {
            return ValidationResult.error("home_name_too_short", 
                "{min_length}", String.valueOf(minLength));
        }
        
        if (homeName.length() > maxLength) {
            return ValidationResult.error("home_name_too_long", 
                "{max_length}", String.valueOf(maxLength));
        }
        
        if (!VALID_HOME_NAME_PATTERN.matcher(homeName).matches()) {
            return ValidationResult.error("home_name_invalid_chars");
        }
        
        return ValidationResult.success();
    }

    /**
     * Validates world restrictions for setting homes.
     */
    public ValidationResult validateWorldRestrictions(@NotNull Player player) {
        World playerWorld = player.getWorld();
        
        if (homeConfig.getBlacklistedWorlds().contains(playerWorld.getName().toLowerCase())) {
            if (!PermissionUtil.hasPermission(player, "homes.bypass.blacklist")) {
                return ValidationResult.error("set_home_blacklisted_world", 
                    "{world_name}", playerWorld.getName());
            }
        }
        
        return ValidationResult.success();
    }

    /**
     * Validates home limits for a player.
     */
    public ValidationResult validateHomeLimit(@NotNull Player player, @NotNull String homeName) {
        // Check if home with this name already exists
        if (corePlugin.getCacheManager() != null) {
            List<Home> cachedHomes = corePlugin.getCacheManager().get(
                HomeModule.PLAYER_HOMES_CACHE_NAME,
                player.getUniqueId(),
                key -> null
            );
            
            if (cachedHomes != null) {
                boolean homeExists = cachedHomes.stream()
                    .anyMatch(h -> h.getHomeName().equalsIgnoreCase(homeName));
                
                if (homeExists) {
                    return ValidationResult.error("home_already_exists", "{home_name}", homeName);
                }
                
                // Check limits using cached count
                return validateHomeLimitInternal(player, cachedHomes.size());
            }
        }
        
        // Fallback to database check
        Home existingHome = dataAccessor.getHome(player.getUniqueId(), homeName);
        if (existingHome != null) {
            return ValidationResult.error("home_already_exists", "{home_name}", homeName);
        }
        
        int currentCount = dataAccessor.getHomeCount(player.getUniqueId());
        return validateHomeLimitInternal(player, currentCount);
    }

    /**
     * Internal method to validate home limits based on current count.
     */
    private ValidationResult validateHomeLimitInternal(@NotNull Player player, int currentCount) {
        int maxHomes = determineMaxHomes(player);
        
        if (maxHomes != -1 && currentCount >= maxHomes) {
            return ValidationResult.error("max_homes_reached", "{max_homes}", String.valueOf(maxHomes));
        }
        
        return ValidationResult.success();
    }

    /**
     * Determines the maximum number of homes a player can have.
     */
    private int determineMaxHomes(@NotNull Player player) {
        // Check for unlimited permission
        if (PermissionUtil.hasPermission(player, 
            homeConfig.getUnlimitedPermissionNode().replace(PermissionUtil.BASE_PERM + ".", ""))) {
            return -1;
        }

        // Dynamic permission checking
        String limitPrefix = homeConfig.getDefaultLimitPermissionPrefix();
        int maxLimit = 0;
        boolean hasAnyLimitPermission = false;

        // Get all effective permissions from the player
        if (player.getEffectivePermissions() != null) {
            for (var permissionAttachmentInfo : player.getEffectivePermissions()) {
                String permission = permissionAttachmentInfo.getPermission();

                if (permission.startsWith(limitPrefix) && permissionAttachmentInfo.getValue()) {
                    String numberPart = permission.substring(limitPrefix.length());
                    
                    try {
                        int limit = Integer.parseInt(numberPart);
                        if (limit > maxLimit) {
                            maxLimit = limit;
                            hasAnyLimitPermission = true;
                        }
                    } catch (NumberFormatException e) {
                        continue;
                    }
                }
            }
        }
        
        // If player has any limit permission, return the highest one
        if (hasAnyLimitPermission) {
            return maxLimit;
        }

        return homeConfig.getDefaultMaxHomes();
    }

    /**
     * Validates if a home exists for deletion.
     */
    public ValidationResult validateHomeExists(@NotNull Player player, @NotNull String homeName) {
        if (corePlugin.getCacheManager() != null) {
            List<Home> cachedHomes = corePlugin.getCacheManager().get(
                HomeModule.PLAYER_HOMES_CACHE_NAME,
                player.getUniqueId(),
                key -> null
            );
            
            if (cachedHomes != null) {
                boolean exists = cachedHomes.stream()
                    .anyMatch(h -> h.getHomeName().equalsIgnoreCase(homeName));
                
                if (!exists) {
                    return ValidationResult.error("home_delete_not_found", "{home_name}", homeName);
                }
                
                return ValidationResult.success();
            }
        }
        
        // Fallback to database
        Home home = dataAccessor.getHome(player.getUniqueId(), homeName);
        if (home == null) {
            return ValidationResult.error("home_delete_not_found", "{home_name}", homeName);
        }
        
        return ValidationResult.success();
    }

    /**
     * Validates home name against reserved names.
     */
    public ValidationResult validateReservedNames(@NotNull String homeName) {
        // List of reserved names that players cannot use
        String[] reservedNames = {"spawn", "admin", "server", "console", "system", "all", "list"};
        
        for (String reserved : reservedNames) {
            if (homeName.equalsIgnoreCase(reserved)) {
                return ValidationResult.error("home_name_reserved", "{home_name}", homeName);
            }
        }
        
        return ValidationResult.success();
    }

    /**
     * Complete validation for setting a home.
     */
    public ValidationResult validateSetHome(@NotNull Player player, @NotNull String homeName) {
        ValidationResult nameValidation = validateHomeName(homeName);
        if (!nameValidation.isSuccess()) return nameValidation;
        
        ValidationResult reservedValidation = validateReservedNames(homeName);
        if (!reservedValidation.isSuccess()) return reservedValidation;
        
        ValidationResult worldValidation = validateWorldRestrictions(player);
        if (!worldValidation.isSuccess()) return worldValidation;
        
        return validateHomeLimit(player, homeName);
    }
} 