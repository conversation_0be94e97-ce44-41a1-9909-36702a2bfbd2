# ===================================================================
#      Messages für das AdventureSMP HomeSystem
# ===================================================================


# ===================================================================
#                       ALLGEMEINE NACHRICHTEN
# ===================================================================
general:
  prefix: "&8[&eAdventureHomes&8] &r"

  # Allgemeine System- und Fehlermeldungen
  no_permission: "&cKeine <PERSON>."
  player_not_found: "&cDer Spieler &e'{player_name}' &cwurde nicht gefunden."
  console_cannot_use: "&cDieser Befehl kann nur von Spielern verwendet werden."
  config_reloaded: "&aKonfiguration für HomeSystem neu geladen."
  internal_error: "&cEin Fehler ist aufgetreten. Bitte kontaktiere einen Administrator."


messages:
  # ===================================================================
  #                       /SETHOME BEFEHL
  # ===================================================================
  home_set_success: "&aDein Home &e'{home_name}' &awurde erfolgreich gesetzt!"
  home_name_too_short: "&cDer Name deines Homes muss mindestens {min_length} Zeichen lang sein."
  home_name_too_long: "&cDer Name deines Homes darf {max_length} Zeichen nicht überschreiten."
  home_name_invalid_chars: "&cDer Name deines Homes enthält ungültige Zeichen."
  home_name_reserved: "&cDer Name &e'{home_name}' &ckann nicht verwendet werden."
  max_homes_reached: "&cDu hast die maximale Anzahl an Home-Punkten erreicht. Limit: &6{max_homes}"
  home_set_blacklisted_world: "&cDu kannst in dieser Welt &8('{world_name}') &ckein Home setzen."
  home_already_exists: "&cDu hast bereits ein Home mit dem Namen &e'{home_name}'&c."


  # ===================================================================
  #                       /DELHOME BEFEHL
  # ===================================================================
  home_delete_success: "&aDein Home &e'{home_name}' &awurde gelöscht."
  home_delete_not_found: "&cDu hast kein Home mit dem Namen &e'{home_name}'&c."


  # ===================================================================
  #                       /HOME BEFEHL
  # ===================================================================
  home_not_found: "&cDas Home mit dem Namen &e'{home_name}' &ckonnte nicht gefunden werden."
  teleport_initiated: "&7Teleportiere zu &e{home_name}&7 in &b{seconds} &7Sekunden... Nicht bewegen!"
  teleport_cancelled_move: "&cTeleportation zu &e{home_name} &cwegen Bewegung abgebrochen."
  teleport_cancelled_other: "&cTeleportation zu &e{home_name} &cabgebrochen."
  teleport_cancelled_damage: "&cTeleportation zu &e{home_name} &cwegen erlittenem Schaden abgebrochen."
  teleport_success: "&aErfolgreich zu &e{home_name}&a teleportiert."
  teleport_cooldown: "&cDu musst &e{seconds} &cSekunden warten, bevor du dich erneut teleportieren kannst."
  home_world_not_found: "&cDie Welt für das Home '{home_name}' konnte nicht gefunden werden. Teleportation fehlgeschlagen."
  no_homes_set: "&cDu hast noch kein Home gesetzt! Nutze /sethome <Name>."
  teleport_already_inprogress: "&cDu teleportierst dich bereits!"


  # ===================================================================
  #                     ADMIN-BEFEHLE (/adminhome)
  # ===================================================================
  admin_home_set_success: "&aHome &e'{home_name}' &afür Spieler &6{player}&a gesetzt."
  admin_home_delete_success: "&aHome &e'{home_name}' &avon Spieler &6{player} &agelöscht."
  admin_all_homes_deleted: "&a&e{count} &aHome-Punkte von Spieler &6{player} &agelöscht."
  admin_teleport_success: "&aZu &e{home_name}&a teleportiert."
  admin_player_no_homes: "&cSpieler &6{player} &chat kein Home gesetzt."
  admin_home_not_found: "&cSpieler &6{player} &chat kein Home mit dem Namen &e'{home_name}'&c."
  admin_import_started: "&eStarte Import von Offline-Spielerdaten... Dies kann einen Moment dauern."
  admin_import_finished: "&aImport der Offline-Spielerdaten abgeschlossen. {count} Spieler verarbeitet."
  admin_import_already_running: "&cDer Spieler-Import wird bereits ausgeführt."
  admin_command_usage: "&6Admin-Befehle für Homes:"
  admin_deleteall_confirm: "&cBist du sicher, dass du alle &e{count} &cHome-Punkte von Spieler &6{player}&c löschen möchtest? Befehl erneut eingeben zur Bestätigung."
  confirmation_expired: "&cBestätigung abgelaufen. Bitte beginne von vorn."
  player_only_command: "&cDieser Befehl kann nur von Spielern verwendet werden."
  console_must_specify_coordinates: "&cDie Konsole muss Koordinaten angeben: /adminhome set <Spieler> <Home> <x> <y> <z> [Welt]"
  world_not_found: "&cWelt &e'{world}' &cnicht gefunden."
  invalid_coordinates: "&cUngültige Koordinaten angegeben."
  command_usage: "&cBenutzung: {command_usage}"