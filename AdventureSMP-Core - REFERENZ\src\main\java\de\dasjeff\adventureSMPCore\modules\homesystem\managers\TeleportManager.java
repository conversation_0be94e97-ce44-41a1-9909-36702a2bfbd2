package de.dasjeff.adventureSMPCore.modules.homesystem.managers;

import de.dasjeff.adventureSMPCore.AdventureSMPCore;
import de.dasjeff.adventureSMPCore.modules.homesystem.HomeModule;
import de.dasjeff.adventureSMPCore.modules.homesystem.config.HomeConfig;
import de.dasjeff.adventureSMPCore.modules.homesystem.config.MessageConfig;
import de.dasjeff.adventureSMPCore.util.PermissionUtil;
import org.bukkit.Location;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

public class TeleportManager {

    private final AdventureSMPCore corePlugin;
    private final HomeModule homeModule;
    private final HomeConfig homeConfig;
    private final MessageConfig messageConfig;

    private final Map<UUID, BukkitTask> countdownTasks = new ConcurrentHashMap<>();
    private final Map<UUID, BukkitTask> movementCheckTasks = new ConcurrentHashMap<>();
    private final Map<UUID, Location> initialTeleportLocations = new ConcurrentHashMap<>();
    private final Map<UUID, Long> teleportCooldowns = new ConcurrentHashMap<>();
    private final Map<UUID, TeleportData> activeTeleports = new ConcurrentHashMap<>();

    public TeleportManager(AdventureSMPCore corePlugin, HomeModule homeModule) {
        this.corePlugin = corePlugin;
        this.homeModule = homeModule;
        this.homeConfig = homeModule.getHomeConfig();
        this.messageConfig = homeModule.getMessageConfig();
    }

    public boolean isTeleporting(Player player) {
        return activeTeleports.containsKey(player.getUniqueId());
    }
    
    public boolean checkAndApplyCooldown(Player player) {
        long currentTime = System.currentTimeMillis();
        UUID playerUuid = player.getUniqueId();
        if (teleportCooldowns.containsKey(playerUuid)) {
            long timeLeft = ((teleportCooldowns.get(playerUuid) + (homeConfig.getTeleportCooldownSeconds() * 1000L)) - currentTime) / 1000L;
            if (timeLeft > 0) {
                messageConfig.sendMessage(player, "teleport_cooldown", "{seconds}", String.valueOf(timeLeft));
                homeModule.playHomeSound(player, homeConfig.getSound("error"));
                return true;
            }
            teleportCooldowns.remove(playerUuid);
        }
        return false;
    }

    public void startTeleport(Player player, Location destination, String destinationName, String successMessageKey, String initiateMessageKey) {
        if (isTeleporting(player)) {
            messageConfig.sendMessage(player, "teleport_already_inprogress");
            homeModule.playHomeSound(player, homeConfig.getSound("error"));
            return;
        }

        if (checkAndApplyCooldown(player)) {
            return;
        }

        int delaySeconds = homeConfig.getTeleportDelaySeconds();

        if (delaySeconds <= 0 || player.hasPermission(PermissionUtil.getFullPermission("homes.bypass.delay"))) {
            executeTeleport(player, destination, destinationName, successMessageKey);
        } else {
            startTeleportWithCountdown(player, destination, destinationName, successMessageKey, delaySeconds);
        }
    }

    private void startTeleportWithCountdown(Player player, Location destination, String destinationName, String successMessageKey, int delaySeconds) {
        UUID playerUuid = player.getUniqueId();
        
        // Store teleport data
        TeleportData teleportData = new TeleportData(destination, destinationName, successMessageKey);
        activeTeleports.put(playerUuid, teleportData);
        initialTeleportLocations.put(playerUuid, player.getLocation().clone());
        
        homeModule.playHomeSound(player, homeConfig.getSound("teleport_initiate"));

        BukkitTask countdownTask = new BukkitRunnable() {
            int remainingSeconds = delaySeconds;
            
            @Override
            public void run() {
                if (!player.isOnline() || !activeTeleports.containsKey(playerUuid)) {
                    cancel();
                    return;
                }
                
                TeleportData data = activeTeleports.get(playerUuid);
                if (data == null) {
                    cancel();
                    return;
                }
                
                if (remainingSeconds > 0) {
                    messageConfig.sendActionBarMessage(player, "teleport_initiated",
                        "{home_name}", data.destinationName, 
                        "{seconds}", String.valueOf(remainingSeconds));
                    remainingSeconds--;
                } else {
                    cancel();
                    if (activeTeleports.containsKey(playerUuid)) {
                        executeTeleport(player, data.destination, data.destinationName, data.successMessageKey);
                        clearTeleportData(playerUuid);
                    }
                }
            }
        }.runTaskTimer(corePlugin, 0L, 20L);
        
        countdownTasks.put(playerUuid, countdownTask);

        // Start movement checking
        if (homeConfig.isCancelTeleportOnMove() && !player.hasPermission(PermissionUtil.getFullPermission("homes.bypass.movecheck"))) {
            BukkitTask movementTask = new BukkitRunnable() {
                @Override
                public void run() {
                    if (!player.isOnline() || !activeTeleports.containsKey(playerUuid)) {
                        cancel();
                        return;
                    }
                    
                    Location currentLocation = player.getLocation();
                    Location initialLocation = initialTeleportLocations.get(playerUuid);
                    
                    if (initialLocation == null) {
                        cancel();
                        return;
                    }
                    
                    TeleportData data = activeTeleports.get(playerUuid);
                    if (data == null) {
                        cancel();
                        return;
                    }
                    
                    // Check if player moved
                    if (currentLocation.getBlockX() != initialLocation.getBlockX() ||
                        currentLocation.getBlockY() != initialLocation.getBlockY() ||
                        currentLocation.getBlockZ() != initialLocation.getBlockZ() ||
                        !currentLocation.getWorld().equals(initialLocation.getWorld())) {
                        
                        cancelTeleportInternal(playerUuid, "teleport_cancelled_move", data.destinationName, false);
                        cancel();
                    }
                }
            }.runTaskTimer(corePlugin, 5L, 5L);
            
            movementCheckTasks.put(playerUuid, movementTask);
        }
    }

    private void executeTeleport(Player player, Location destination, String destinationName, String successMessageKey) {
        player.teleport(destination);
        messageConfig.sendMessage(player, successMessageKey, "{home_name}", destinationName);
        homeModule.playHomeSound(player, homeConfig.getSound("teleport_success"));
        
        if (homeConfig.getTeleportCooldownSeconds() > 0 && !player.hasPermission(PermissionUtil.getFullPermission("homes.bypass.cooldown"))) {
            teleportCooldowns.put(player.getUniqueId(), System.currentTimeMillis());
        }
    }

    private void cancelTeleportInternal(UUID playerUuid, String reasonKey, String destinationName, boolean applyCooldownOnCancel) {
        Player player = corePlugin.getServer().getPlayer(playerUuid);
        
        if (player != null && player.isOnline()) {
            messageConfig.sendMessage(player, reasonKey, "{home_name}", destinationName);
            homeModule.playHomeSound(player, homeConfig.getSound("teleport_cancelled"));

            if (applyCooldownOnCancel && homeConfig.getTeleportCooldownSeconds() > 0 && !player.hasPermission(PermissionUtil.getFullPermission("homes.bypass.cooldown"))) {
                teleportCooldowns.put(playerUuid, System.currentTimeMillis());
            }
        }
        
        clearTeleportData(playerUuid);
    }
    
    public void cancelTeleportForDamage(Player player) {
        UUID playerUuid = player.getUniqueId();
        if (isTeleporting(player)) {
            TeleportData data = activeTeleports.get(playerUuid);
            String destinationName = data != null ? data.destinationName : "your destination";
            cancelTeleportInternal(playerUuid, "teleport_cancelled_damage", destinationName, false);
        }
    }

    public void cancelTeleportForQuit(Player player) {
        UUID playerUuid = player.getUniqueId();
        if (isTeleporting(player)) {
            TeleportData data = activeTeleports.get(playerUuid);
            String destinationName = data != null ? data.destinationName : "your destination";
            cancelTeleportInternal(playerUuid, "teleport_cancelled_other", destinationName, false);
        }
    }

    private void clearTeleportData(UUID playerUuid) {
        activeTeleports.remove(playerUuid);
        initialTeleportLocations.remove(playerUuid);

        BukkitTask countdownTask = countdownTasks.remove(playerUuid);
        if (countdownTask != null && !countdownTask.isCancelled()) {
            countdownTask.cancel();
        }

        BukkitTask movementTask = movementCheckTasks.remove(playerUuid);
        if (movementTask != null && !movementTask.isCancelled()) {
            movementTask.cancel();
        }
    }

    /**
         * Data class to store teleport information
         */
        private record TeleportData(Location destination, String destinationName, String successMessageKey) {
    }
} 