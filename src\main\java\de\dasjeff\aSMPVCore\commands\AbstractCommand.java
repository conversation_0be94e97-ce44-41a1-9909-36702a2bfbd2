package de.dasjeff.aSMPVCore.commands;

import com.velocitypowered.api.command.Command;
import com.velocitypowered.api.command.CommandSource;
import com.velocitypowered.api.proxy.Player;
import de.dasjeff.aSMPVCore.ASMPVCore;
import de.dasjeff.aSMPVCore.util.MessageUtil;
import net.kyori.adventure.text.Component;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * Abstract base class for all ASMP-VCore commands.
 * Provides common functionality and structure for command implementations.
 */
public abstract class AbstractCommand implements Command {

    protected final ASMPVCore corePlugin;
    protected final String commandName;
    protected final String permission;
    protected final String description;

    /**
     * Constructor for AbstractCommand.
     * @param corePlugin The main plugin instance.
     * @param commandName The name of the command.
     * @param permission The permission required to execute the command.
     * @param description The description of the command.
     */
    protected AbstractCommand(ASMPVCore corePlugin, String commandName, String permission, String description) {
        this.corePlugin = corePlugin;
        this.commandName = commandName;
        this.permission = permission;
        this.description = description;
    }

    @Override
    public void execute(Command.Invocation invocation) {
        CommandSource source = invocation.source();
        String[] args = invocation.arguments();

        // Check permission
        if (permission != null && !permission.isEmpty() && !source.hasPermission(permission)) {
            sendMessage(source, MessageUtil.prefixedError("You don't have permission to use this command."));
            return;
        }

        // Security check for players
        if (source instanceof Player player) {
            if (!corePlugin.getSecurityManager().canExecuteCommand(player.getUniqueId())) {
                sendMessage(source, MessageUtil.prefixedError("Please wait before using another command."));
                return;
            }
        }

        try {
            executeCommand(source, args);
        } catch (Exception e) {
            corePlugin.getLogger().error("Error executing command '{}' by {}", commandName, source, e);
            sendMessage(source, MessageUtil.prefixedError("An error occurred while executing the command."));
        }
    }

    @Override
    public CompletableFuture<List<String>> suggestAsync(Command.Invocation invocation) {
        CommandSource source = invocation.source();
        String[] args = invocation.arguments();

        // Check permission for tab completion
        if (permission != null && !permission.isEmpty() && !source.hasPermission(permission)) {
            return CompletableFuture.completedFuture(List.of());
        }

        try {
            return CompletableFuture.completedFuture(getTabCompletions(source, args));
        } catch (Exception e) {
            corePlugin.getLogger().error("Error getting tab completions for command '{}' by {}", commandName, source, e);
            return CompletableFuture.completedFuture(List.of());
        }
    }

    /**
     * Executes the command logic.
     * @param source The command source.
     * @param args The command arguments.
     */
    protected abstract void executeCommand(CommandSource source, String[] args);

    /**
     * Gets tab completions for the command.
     * @param source The command source.
     * @param args The current command arguments.
     * @return A list of tab completions.
     */
    protected List<String> getTabCompletions(CommandSource source, String[] args) {
        return List.of();
    }

    /**
     * Sends a message to the command source.
     * @param source The command source.
     * @param message The message to send.
     */
    protected void sendMessage(CommandSource source, Component message) {
        source.sendMessage(message);
    }

    /**
     * Sends a message to the command source.
     * @param source The command source.
     * @param message The message to send.
     */
    protected void sendMessage(CommandSource source, String message) {
        source.sendMessage(MessageUtil.formatLegacy(message));
    }

    /**
     * Checks if the command source is a player.
     * @param source The command source.
     * @return true if the source is a player, false otherwise.
     */
    protected boolean isPlayer(CommandSource source) {
        return source instanceof Player;
    }

    /**
     * Gets the player from the command source if it's a player.
     * @param source The command source.
     * @return The player, or null if not a player.
     */
    protected Player getPlayer(CommandSource source) {
        return isPlayer(source) ? (Player) source : null;
    }

    /**
     * Checks if the command source is a player and sends an error if not.
     * @param source The command source.
     * @return true if the source is a player, false otherwise.
     */
    protected boolean requirePlayer(CommandSource source) {
        if (!isPlayer(source)) {
            sendMessage(source, MessageUtil.prefixedError("This command can only be used by players."));
            return false;
        }
        return true;
    }

    /**
     * Checks if the command source is the console and sends an error if not.
     * @param source The command source.
     * @return true if the source is the console, false otherwise.
     */
    protected boolean requireConsole(CommandSource source) {
        if (isPlayer(source)) {
            sendMessage(source, MessageUtil.prefixedError("This command can only be used from the console."));
            return false;
        }
        return true;
    }

    /**
     * Validates the number of arguments.
     * @param source The command source.
     * @param args The command arguments.
     * @param expectedCount The expected number of arguments.
     * @return true if the argument count is correct, false otherwise.
     */
    protected boolean validateArgCount(CommandSource source, String[] args, int expectedCount) {
        if (args.length != expectedCount) {
            sendMessage(source, MessageUtil.prefixedError("Invalid number of arguments. Expected: " + expectedCount));
            sendUsage(source);
            return false;
        }
        return true;
    }

    /**
     * Validates the minimum number of arguments.
     * @param source The command source.
     * @param args The command arguments.
     * @param minCount The minimum number of arguments.
     * @return true if the argument count is sufficient, false otherwise.
     */
    protected boolean validateMinArgCount(CommandSource source, String[] args, int minCount) {
        if (args.length < minCount) {
            sendMessage(source, MessageUtil.prefixedError("Not enough arguments. Minimum required: " + minCount));
            sendUsage(source);
            return false;
        }
        return true;
    }

    /**
     * Sends the command usage to the source.
     * @param source The command source.
     */
    protected void sendUsage(CommandSource source) {
        sendMessage(source, MessageUtil.prefixedInfo("Usage: " + getUsage()));
    }

    /**
     * Gets the usage string for this command.
     * @return The usage string.
     */
    protected abstract String getUsage();

    /**
     * Executes a command asynchronously.
     * @param task The task to execute.
     */
    protected void executeAsync(Runnable task) {
        corePlugin.getProxyServer().getScheduler()
                .buildTask(corePlugin, task)
                .schedule();
    }

    /**
     * Executes a command asynchronously with a callback.
     * @param task The task to execute.
     * @param callback The callback to execute after the task completes.
     */
    protected void executeAsync(Runnable task, Runnable callback) {
        corePlugin.getProxyServer().getScheduler()
                .buildTask(corePlugin, () -> {
                    try {
                        task.run();
                    } finally {
                        if (callback != null) {
                            callback.run();
                        }
                    }
                })
                .schedule();
    }

    // Getters
    public String getCommandName() {
        return commandName;
    }

    public String getPermission() {
        return permission;
    }

    public String getDescription() {
        return description;
    }
}
