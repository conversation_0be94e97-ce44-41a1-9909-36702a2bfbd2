package de.dasjeff.adventureSMPCore.modules.homesystem.config;

import de.dasjeff.adventureSMPCore.AdventureSMPCore;
import de.dasjeff.adventureSMPCore.modules.homesystem.HomeModule;
import de.dasjeff.adventureSMPCore.util.ChatUtil;
import org.bukkit.Material;
import org.bukkit.configuration.file.FileConfiguration;

import java.util.List;

public class GuiConfig {

    private final AdventureSMPCore corePlugin;
    private FileConfiguration config;

    private String homeListTitle;
    private int homeListRows;
    private Material homeItemMaterial;
    private Material nextPageItemMaterial;
    private Material prevPageItemMaterial;
    private Material deleteConfirmItemMaterial;
    private Material deleteCancelItemMaterial;
    private Material fillEmptySlotsMaterial;
    private boolean fillEmptySlots;
    private String fillEmptySlotsName;

    private boolean homeItemGlow;
    private int homeItemCustomModelData;

    private int prevPageSlot;
    private int nextPageSlot;

    private int deleteConfirmSlot;
    private int deleteCancelSlot;
    private boolean deleteConfirmFillEmpty;
    private Material deleteConfirmFillMaterial;
    private boolean deleteConfirmItemGlow;
    private boolean deleteCancelItemGlow;

    private String homeItemName;
    private List<String> homeItemLore;
    private String nextPageItemName;
    private String prevPageItemName;
    private String deleteConfirmTitle;
    private String deleteConfirmItemName;
    private List<String> deleteConfirmItemLore;
    private String deleteCancelItemName;


    public GuiConfig(AdventureSMPCore corePlugin) {
        this.corePlugin = corePlugin;
        loadConfig();
    }

    private void loadConfig() {
        setDefaults();
        
        config = corePlugin.getConfigManager().loadCustomConfig(HomeModule.MODULE_NAME, "gui.yml");
        if (config == null) {
            corePlugin.getLogger().severe("[" + HomeModule.MODULE_NAME + "] gui.yml could not be loaded! Using default values.");
            return;
        }

        // GUI settings
        this.homeListTitle = config.getString("home_list.title", this.homeListTitle);
        this.homeListRows = Math.max(1, Math.min(6, config.getInt("home_list.rows", this.homeListRows)));
        
        // Materials
        this.homeItemMaterial = loadMaterial("home_list.item.material", this.homeItemMaterial);
        this.nextPageItemMaterial = loadMaterial("home_list.next_page_item.material", this.nextPageItemMaterial);
        this.prevPageItemMaterial = loadMaterial("home_list.prev_page_item.material", this.prevPageItemMaterial);
        this.deleteConfirmItemMaterial = loadMaterial("delete_confirmation.confirm_item.material", this.deleteConfirmItemMaterial);
        this.deleteCancelItemMaterial = loadMaterial("delete_confirmation.cancel_item.material", this.deleteCancelItemMaterial);
        this.fillEmptySlotsMaterial = loadMaterial("home_list.fill_empty_slots.material", this.fillEmptySlotsMaterial);
        this.deleteConfirmFillMaterial = loadMaterial("delete_confirmation.fill_material", this.deleteConfirmFillMaterial);

        // Booleans and Numbers
        this.fillEmptySlots = config.getBoolean("home_list.fill_empty_slots.enabled", this.fillEmptySlots);
        this.homeItemGlow = config.getBoolean("home_list.item.glow", this.homeItemGlow);
        this.homeItemCustomModelData = config.getInt("home_list.item.custom_model_data", this.homeItemCustomModelData);
        this.prevPageSlot = config.getInt("home_list.prev_page_item.slot", this.prevPageSlot);
        this.nextPageSlot = config.getInt("home_list.next_page_item.slot", this.nextPageSlot);
        this.deleteConfirmSlot = config.getInt("delete_confirmation.confirm_item.slot", this.deleteConfirmSlot);
        this.deleteCancelSlot = config.getInt("delete_confirmation.cancel_item.slot", this.deleteCancelSlot);
        this.deleteConfirmFillEmpty = config.getBoolean("delete_confirmation.fill_empty_slots.enabled", this.deleteConfirmFillEmpty);
        this.deleteConfirmItemGlow = config.getBoolean("delete_confirmation.confirm_item.glow", this.deleteConfirmItemGlow);
        this.deleteCancelItemGlow = config.getBoolean("delete_confirmation.cancel_item.glow", this.deleteCancelItemGlow);

        // Strings
        this.fillEmptySlotsName = config.getString("home_list.fill_empty_slots.name", this.fillEmptySlotsName);
        this.homeItemName = config.getString("home_list.item.name", this.homeItemName);
        this.nextPageItemName = config.getString("home_list.next_page_item.name", this.nextPageItemName);
        this.prevPageItemName = config.getString("home_list.prev_page_item.name", this.prevPageItemName);
        this.deleteConfirmTitle = config.getString("delete_confirmation.title", this.deleteConfirmTitle);
        this.deleteConfirmItemName = config.getString("delete_confirmation.confirm_item.name", this.deleteConfirmItemName);
        this.deleteCancelItemName = config.getString("delete_confirmation.cancel_item.name", this.deleteCancelItemName);
        
        // String Lists
        this.homeItemLore = loadStringList("home_list.item.lore", this.homeItemLore);
        this.deleteConfirmItemLore = loadStringList("delete_confirmation.confirm_item.lore", this.deleteConfirmItemLore);
    }
    
    private void setDefaults() {
        this.homeListRows = 3;
        this.homeItemMaterial = Material.PAPER;
        this.nextPageItemMaterial = Material.ARROW;
        this.prevPageItemMaterial = Material.ARROW;
        this.deleteConfirmItemMaterial = Material.RED_WOOL;
        this.deleteCancelItemMaterial = Material.GREEN_WOOL;
        this.fillEmptySlotsMaterial = Material.GRAY_STAINED_GLASS_PANE;
        this.fillEmptySlots = true;
        this.fillEmptySlotsName = " ";
        this.homeItemGlow = false;
        this.homeItemCustomModelData = 0;
        this.prevPageSlot = 0;
        this.nextPageSlot = 8;
        this.deleteConfirmSlot = 2;
        this.deleteCancelSlot = 6;
        this.deleteConfirmFillEmpty = true;
        this.deleteConfirmFillMaterial = Material.BLACK_STAINED_GLASS_PANE;
        this.deleteConfirmItemGlow = false;
        this.deleteCancelItemGlow = false;

        this.homeItemName = "&b{home_name}";
        this.homeItemLore = List.of(
            "&7Welt: &f{world_name}",
            "&7Koordinaten: &fX:{x} Y:{y} Z:{z}",
            " ",
            "&eLinksklick zum Teleportieren.",
            "&cRechtsklick zum Löschen."
        );
        this.nextPageItemName = "&aNächste Seite ->";
        this.prevPageItemName = "&a<- Vorherige Seite";
        this.deleteConfirmTitle = "&4Löschen bestätigen: {home_name}";
        this.deleteConfirmItemName = "&cLÖSCHEN VON '{home_name}' BESTÄTIGEN";
        this.deleteConfirmItemLore = List.of("&7Diese Aktion kann nicht rückgängig gemacht werden.");
        this.deleteCancelItemName = "&aABBRECHEN";
    }

    public void reloadConfig() {
        corePlugin.getConfigManager().loadCustomConfig(HomeModule.MODULE_NAME, "gui.yml");
        loadConfig();
    }

    /**
     * Gets a GUI message with placeholder replacements
     */
    public String getGuiMessage(String key, String... replacements) {
        String message = switch (key) {
            case "gui_title" -> homeListTitle;
            case "gui_item_name" -> homeItemName;
            case "gui_next_page_item_name" -> nextPageItemName;
            case "gui_prev_page_item_name" -> prevPageItemName;
            case "gui_delete_confirm_title" -> deleteConfirmTitle;
            case "gui_delete_confirm_item_name" -> deleteConfirmItemName;
            case "gui_delete_cancel_item_name" -> deleteCancelItemName;
            default -> "&cMissing GUI message for key: " + key;
        };
        
        if (replacements != null) {
            for (int i = 0; i < replacements.length; i += 2) {
                if (i + 1 < replacements.length) {
                    message = message.replace(replacements[i], replacements[i + 1]);
                }
            }
        }
        return ChatUtil.formatLegacy(message);
    }
    
    /**
     * Gets a GUI message list with placeholder replacements
     */
    public List<String> getGuiMessageList(String key, String... replacements) {
        List<String> messageList = switch (key) {
            case "gui_item_lore" -> homeItemLore;
            case "gui_delete_confirm_item_lore" -> deleteConfirmItemLore;
            default -> List.of("&cMissing GUI message list for key: " + key);
        };
        
        return messageList.stream()
            .map(line -> {
                String processedLine = line;
                if (replacements != null) {
                    for (int i = 0; i < replacements.length; i += 2) {
                        if (i + 1 < replacements.length) {
                            processedLine = processedLine.replace(replacements[i], replacements[i + 1]);
                        }
                    }
                }
                return ChatUtil.formatLegacy(processedLine);
            })
            .toList();
    }

    // Getters
    public String getHomeListTitle() { return homeListTitle; }
    public int getHomeListRows() { return homeListRows; }
    public Material getHomeItemMaterial() { return homeItemMaterial; }
    public Material getNextPageItemMaterial() { return nextPageItemMaterial; }
    public Material getPrevPageItemMaterial() { return prevPageItemMaterial; }
    public Material getDeleteConfirmItemMaterial() { return deleteConfirmItemMaterial; }
    public Material getDeleteCancelItemMaterial() { return deleteCancelItemMaterial; }
    public boolean isFillEmptySlots() { return fillEmptySlots; }
    public Material getFillEmptySlotsMaterial() { return fillEmptySlotsMaterial; }
    public String getFillEmptySlotsName() { return fillEmptySlotsName; }

    public boolean isHomeItemGlow() { return homeItemGlow; }
    public int getHomeItemCustomModelData() { return homeItemCustomModelData; }

    public int getPrevPageSlot() { return prevPageSlot; }
    public int getNextPageSlot() { return nextPageSlot; }
    
    public int getDeleteConfirmSlot() { return deleteConfirmSlot; }
    public int getDeleteCancelSlot() { return deleteCancelSlot; }
    public boolean isDeleteConfirmFillEmpty() { return deleteConfirmFillEmpty; }
    public Material getDeleteConfirmFillMaterial() { return deleteConfirmFillMaterial; }
    public boolean isDeleteConfirmItemGlow() { return deleteConfirmItemGlow; }
    public boolean isDeleteCancelItemGlow() { return deleteCancelItemGlow; }

    /**
     * Helper method to load a Material from config
     */
    private Material loadMaterial(String path, Material defaultMaterial) {
        String materialName = config.getString(path, "");
        if (materialName.isEmpty()) return defaultMaterial;
        
        Material material = Material.matchMaterial(materialName);
        return material != null ? material : defaultMaterial;
    }
    
    /**
     * Helper method to load a String list from config
     */
    private List<String> loadStringList(String path, List<String> defaultList) {
        List<String> configList = config.getStringList(path);
        return configList.isEmpty() ? defaultList : configList;
    }
} 