package de.dasjeff.aSMPVCore;

import com.google.inject.Inject;
import com.velocitypowered.api.event.proxy.ProxyInitializeEvent;
import com.velocitypowered.api.event.Subscribe;
import com.velocitypowered.api.plugin.Plugin;
import org.slf4j.Logger;

@Plugin(id = "asmp-vcore", name = "ASMP-VCore", version = BuildConstants.VERSION, description = "Verwaltet netzwerkweite Systeme und Kommunikation zwischen den Servern.", authors = {"DasJeff"})
public class ASMPVCore {

    @Inject
    private Logger logger;

    @Subscribe
    public void onProxyInitialization(ProxyInitializeEvent event) {
    }
}
