package de.dasjeff.adventureSMPCore.modules.homesystem.commands;

import de.dasjeff.adventureSMPCore.AdventureSMPCore;
import de.dasjeff.adventureSMPCore.modules.homesystem.HomeModule;
import de.dasjeff.adventureSMPCore.modules.homesystem.managers.TeleportManager;
import de.dasjeff.adventureSMPCore.modules.homesystem.model.Home;
import de.dasjeff.adventureSMPCore.util.PermissionUtil;
import org.bukkit.Location;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

import java.util.Collections;
import java.util.List;

public class HomeCommand extends BaseHomeCommand {

    private final TeleportManager teleportManager;

    public HomeCommand(AdventureSMPCore corePlugin, HomeModule homeModule) {
        super(corePlugin, homeModule,
                "home",
                PermissionUtil.getFullPermission("homes.home"),
                true,
                "Teleports you to one of your homes or opens the home GUI.",
                "/home [HomeName]",
                Collections.singletonList("h"));
        this.teleportManager = homeModule.getTeleportManager();
    }

    @Override
    protected boolean executeCommand(CommandSender sender, String[] args) {
        Player player = (Player) sender;

        if (args.length == 0) {
            return handleNoArguments(player);
        }

        return handleSpecificHome(player, args[0]);
    }

    private boolean handleNoArguments(Player player) {
        List<Home> homes = homeService.getHomesImmediate(player.getUniqueId());
        
        if (!homes.isEmpty()) {
            processHomesResult(player, homes);
            return true;
        }

        // If no cached homes, check database synchronously
        executeAsync(
            getHomesAsync(player),
            loadedHomes -> processHomesResult(player, loadedHomes)
        );

        return true;
    }

    private boolean handleSpecificHome(Player player, String homeName) {
        Home home = homeService.findHomeImmediate(player.getUniqueId(), homeName);
        
        if (home != null) {
            teleportToHome(player, home);
        } else {
            sendMessage(player, "home_not_found", "{home_name}", homeName);
            playSound(player, homeModule.getHomeConfig().getSound("error"));
        }

        return true;
    }

    private void processHomesResult(Player player, List<Home> homes) {
        if (!checkHasHomes(player, homes)) {
            return;
        }

        if (homes.size() == 1 && homeConfig.isTeleportToOnlyHome()) {
            teleportToHome(player, homes.getFirst());
        } else {
            openHomeGUI(player);
        }
    }

    private void teleportToHome(Player player, Home home) {
        Location location = home.toLocation();
        if (location == null) {
            sendMessage(player, "home_world_not_found", "{home_name}", home.getHomeName());
            playSound(player, homeConfig.getSound("error"));
            return;
        }

        teleportManager.startTeleport(
            player, 
            location, 
            home.getHomeName(), 
            "teleport_success", 
            "teleport_initiated"
        );
    }

    private void openHomeGUI(Player player) {
        if (homeModule.getHomeGUIManager() != null) {
            homeModule.getHomeGUIManager().openHomeListGUI(player, 1);
        } else {
            sendMessage(player, "internal_error");
            playSound(player, "error");
            corePlugin.getLogger().warning("HomeGUIManager is null in HomeCommand!");
        }
    }

    @Override
    public List<String> tabComplete(CommandSender sender, String[] args) {
        if (!(sender instanceof Player player) || args.length > 1) {
            return Collections.emptyList();
        }

        String currentArg = args.length == 1 ? args[0] : "";
        return getHomeNameCompletions(player, currentArg);
    }
} 