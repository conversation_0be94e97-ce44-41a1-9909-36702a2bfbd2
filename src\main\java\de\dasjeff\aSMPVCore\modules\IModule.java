package de.dasjeff.aSMPVCore.modules;

import de.dasjeff.aSMPVCore.ASMPVCore;

/**
 * Interface for all modules in the ASMP-VCore plugin.
 * Modules provide modular functionality that can be loaded, enabled, and disabled independently.
 */
public interface IModule {

    /**
     * Gets the unique name of the module.
     * @return The name of the module.
     */
    String getName();

    /**
     * Called when the plugin is loading.
     * This is where modules should initialize their configurations and prepare for enabling.
     * @param corePlugin The main plugin instance.
     */
    void onLoad(ASMPVCore corePlugin);

    /**
     * Called when the module is being enabled.
     * This is where modules should register commands, listeners, and start their functionality.
     * @param corePlugin The main plugin instance.
     */
    void onEnable(ASMPVCore corePlugin);

    /**
     * Called when the module is being disabled.
     * This is where modules should clean up resources, unregister listeners, and shutdown gracefully.
     */
    void onDisable();

    /**
     * Gets the version of this module.
     * @return The module version.
     */
    default String getVersion() {
        return "1.0";
    }

    /**
     * Gets the description of this module.
     * @return The module description.
     */
    default String getDescription() {
        return "No description provided.";
    }

    /**
     * Checks if this module is enabled.
     * @return true if the module is enabled, false otherwise.
     */
    default boolean isEnabled() {
        return true;
    }
}
