package de.dasjeff.adventureSMPCore.util;

import net.kyori.adventure.text.Component;
import org.bukkit.Material;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.inventory.ItemFlag;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class ItemBuilder {

    private final ItemStack itemStack;
    private final ItemMeta itemMeta;

    public ItemBuilder(Material material) {
        this.itemStack = new ItemStack(material);
        this.itemMeta = this.itemStack.getItemMeta();
    }

    public ItemBuilder(ItemStack itemStack) {
        this.itemStack = itemStack.clone();
        this.itemMeta = this.itemStack.getItemMeta();
    }

    public ItemBuilder setName(String name) {
        if (name != null && !name.isEmpty()) {
            this.itemMeta.displayName(ChatUtil.convertLegacyToComponent(name));
        } else if (name != null) {
            this.itemMeta.displayName(Component.empty());
        }
        return this;
    }

    public ItemBuilder setAmount(int amount) {
        this.itemStack.setAmount(amount);
        return this;
    }

    public ItemBuilder setLore(List<String> lore) {
        if (lore != null && !lore.isEmpty()) {
            List<Component> componentLore = lore.stream()
                                               .map(ChatUtil::convertLegacyToComponent)
                                               .collect(Collectors.toList());
            this.itemMeta.lore(componentLore);
        } else if (lore != null) {
            this.itemMeta.lore(new ArrayList<>());
        }
        return this;
    }

    public ItemBuilder setLore(String... loreLines) {
        if (loreLines != null && loreLines.length > 0) {
            return setLore(Arrays.asList(loreLines));
        } else if (loreLines != null) {
            return setLore(new ArrayList<String>());
        }
        return this;
    }

    public ItemBuilder addLoreLine(String line) {
        if (line != null) {
            List<Component> currentLore = this.itemMeta.lore();
            List<Component> newLore = (currentLore == null) ? new ArrayList<>() : new ArrayList<>(currentLore);
            newLore.add(ChatUtil.convertLegacyToComponent(line));
            this.itemMeta.lore(newLore);
        }
        return this;
    }
    
    public ItemBuilder setCustomModelData(int customModelData) {
        this.itemMeta.setCustomModelData(customModelData);
        return this;
    }

    public ItemBuilder addEnchant(Enchantment enchantment, int level, boolean ignoreLevelRestriction) {
        this.itemMeta.addEnchant(enchantment, level, ignoreLevelRestriction);
        return this;
    }

    public ItemBuilder removeEnchant(Enchantment enchantment) {
        this.itemMeta.removeEnchant(enchantment);
        return this;
    }

    public ItemBuilder setGlowing(boolean glowing) {
        if (glowing) {
            this.itemMeta.addEnchant(Enchantment.LURE, 1, false);
            this.itemMeta.addItemFlags(ItemFlag.HIDE_ENCHANTS);
        } else {
            if (this.itemMeta.hasEnchant(Enchantment.LURE) && this.itemMeta.getEnchantLevel(Enchantment.LURE) == 1 && this.itemMeta.hasItemFlag(ItemFlag.HIDE_ENCHANTS)){
                 this.itemMeta.removeEnchant(Enchantment.LURE);
                 this.itemMeta.removeItemFlags(ItemFlag.HIDE_ENCHANTS);
            }
        }
        return this;
    }

    public ItemBuilder addItemFlags(ItemFlag... flags) {
        this.itemMeta.addItemFlags(flags);
        return this;
    }

    public ItemBuilder removeItemFlags(ItemFlag... flags) {
        this.itemMeta.removeItemFlags(flags);
        return this;
    }
    
    public ItemBuilder setUnbreakable(boolean unbreakable) {
        this.itemMeta.setUnbreakable(unbreakable);
        return this;
    }

    public ItemStack build() {
        this.itemStack.setItemMeta(this.itemMeta);
        return this.itemStack;
    }
} 