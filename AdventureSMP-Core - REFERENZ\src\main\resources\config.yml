# ===================================================================
#         Konfiguration für das AdventureSMP-Core Plugin
# ===================================================================


# ===================================================================
#                            DATENBANK
# ===================================================================
# Verbindungsdetails für die MySQL-Datenbank.
database:
  host: "localhost"
  port: 3306
  database: "adventuresmp_core"
  username: "root"
  password: "your_password"
  useSSL: false

  # --- Verbindungs-Pool Einstellungen (für Experten) ---
  # Der Pool verwaltet Datenbankverbindungen, um die Leistung zu optimieren.
  # Maximale Anzahl an gleichzeitigen Verbindungen zur Datenbank.
  poolSize: 10
  # Zeitlimit für den Verbindungsaufbau in Millisekunden.
  connectionTimeout: 30000
  # Zeit, nach der eine inaktive Verbindung geschlossen wird in Millisekunden.
  idleTimeout: 600000
  # Maximale Lebensdauer einer Verbindung, bevor sie erneuert wird in Millisekunden.
  maxLifetime: 1800000


# ===================================================================
#                       CACHE-SYSTEM
# ===================================================================
# Konfiguration für den Cache zur Leistungsverbesserung.
cache:
  # Aktiviert die Sammlung von Cache-Statistiken.
  enable_statistics: true
  # Standardmäßige maximale Anzahl an Einträgen in einem Cache.
  default_max_size: 1000
  # Standard-Ablaufzeit für Cache-Einträge in Minuten.
  default_expire_minutes: 30

  # --- Spezifische Cache-Einstellungen (überschreibt die Standardwerte) ---
  caches:
    playerHomesCache:
      max_size: 500
      expire_minutes: 10
    adminHomeAllPlayersCache:
      max_size: 2000
      expire_minutes: 5
    adminHomePlayersWithHomesCache:
      max_size: 1000
      expire_minutes: 3


# ===================================================================
#                  ALLGEMEINE & MODUL-EINSTELLUNGEN
# ===================================================================
# Aktiviert den Debug-Modus.
debug_mode: false
# Aktiviert Server-Statistiken mit bStats.
enable_metrics: true

# --- Modul-Konfiguration ---
# Liste der Module, die geladen werden sollen.
modules:
  enabled:
    - "HomeSystem"


# ===================================================================
#                             SICHERHEIT
# ===================================================================
security:
  # Maximale Anzahl an asynchronen Operationen pro Spieler.
  max_async_operations_per_player: 10
  # Mindestzeit in Millisekunden zwischen zwei Befehlen eines Spielers.
  command_cooldown_ms: 100


# ===================================================================
#                              LEISTUNG
# ===================================================================
performance:
  # Anzahl der Threads für asynchrone Aufgaben.
  async_thread_pool_size: 4
  # Größe für Stapelverarbeitungs-Operationen (z.B. beim Speichern vieler Daten).
  batch_operation_size: 100
  # Intervall in Minuten, in dem abgelaufene Cache-Einträge entfernt werden.
  cache_cleanup_interval_minutes: 30


# ===================================================================
#                           PLUGIN-PRÄFIX
# ===================================================================
# Standard-Präfix, der vor Plugin-Nachrichten angezeigt wird.
# Kann von Modulen überschrieben werden.
plugin_prefix: "&8[&6AdventureSMP&8] &r"