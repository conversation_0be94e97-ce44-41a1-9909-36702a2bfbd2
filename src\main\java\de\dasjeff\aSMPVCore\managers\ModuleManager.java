package de.dasjeff.aSMPVCore.managers;

import de.dasjeff.aSMPVCore.ASMPVCore;
import de.dasjeff.aSMPVCore.modules.IModule;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

/**
 * Manages modules for the ASMP-VCore plugin.
 * Handles module registration, loading, enabling, disabling, and lifecycle management.
 */
public class ModuleManager {

    private final ASMPVCore corePlugin;
    private final Map<String, IModule> modules = new LinkedHashMap<>();
    private final Map<String, ModuleState> moduleStates = new LinkedHashMap<>();

    public ModuleManager(ASMPVCore corePlugin) {
        this.corePlugin = corePlugin;
    }

    /**
     * Registers a module with the ModuleManager.
     * @param module The module instance to register.
     */
    public void registerModule(IModule module) {
        if (modules.containsKey(module.getName().toLowerCase())) {
            corePlugin.getLogger().warn("Module with name '{}' is already registered. Skipping.", module.getName());
            return;
        }
        
        modules.put(module.getName().toLowerCase(), module);
        moduleStates.put(module.getName().toLowerCase(), ModuleState.REGISTERED);
        corePlugin.getLogger().info("Registered module: {} v{}", module.getName(), module.getVersion());
    }

    /**
     * Loads all registered modules.
     * Calls the onLoad() method of each module.
     */
    public void loadModules() {
        corePlugin.getLogger().info("Loading all registered modules...");
        
        for (Map.Entry<String, IModule> entry : modules.entrySet()) {
            String moduleName = entry.getKey();
            IModule module = entry.getValue();
            
            if (moduleStates.get(moduleName) != ModuleState.REGISTERED) {
                continue;
            }
            
            try {
                corePlugin.getLogger().info("Loading module: {}...", module.getName());
                module.onLoad(corePlugin);
                moduleStates.put(moduleName, ModuleState.LOADED);
                corePlugin.getLogger().info("Module loaded: {}", module.getName());
            } catch (Exception e) {
                corePlugin.getLogger().error("Error loading module: {}", module.getName(), e);
                moduleStates.put(moduleName, ModuleState.ERROR);
            }
        }
        
        corePlugin.getLogger().info("All registered modules have been processed for loading.");
    }

    /**
     * Enables all loaded modules.
     * Calls the onEnable() method of each module.
     */
    public void enableModules() {
        corePlugin.getLogger().info("Enabling all loaded modules...");
        
        for (Map.Entry<String, IModule> entry : modules.entrySet()) {
            String moduleName = entry.getKey();
            IModule module = entry.getValue();
            
            if (moduleStates.get(moduleName) != ModuleState.LOADED) {
                continue;
            }
            
            try {
                corePlugin.getLogger().info("Enabling module: {}...", module.getName());
                module.onEnable(corePlugin);
                moduleStates.put(moduleName, ModuleState.ENABLED);
                corePlugin.getLogger().info("Module enabled: {}", module.getName());
            } catch (Exception e) {
                corePlugin.getLogger().error("Error enabling module: {}", module.getName(), e);
                moduleStates.put(moduleName, ModuleState.ERROR);
            }
        }
        
        corePlugin.getLogger().info("All loaded modules have been processed for enabling.");
    }

    /**
     * Disables all enabled modules.
     * Calls the onDisable() method of each module in reverse order.
     */
    public void disableModules() {
        corePlugin.getLogger().info("Disabling all enabled modules...");
        
        // Convert to array and iterate in reverse order
        IModule[] modulesArray = modules.values().toArray(new IModule[0]);
        String[] moduleNames = modules.keySet().toArray(new String[0]);
        
        for (int i = modulesArray.length - 1; i >= 0; i--) {
            IModule module = modulesArray[i];
            String moduleName = moduleNames[i];
            
            if (moduleStates.get(moduleName) != ModuleState.ENABLED) {
                continue;
            }
            
            try {
                corePlugin.getLogger().info("Disabling module: {}...", module.getName());
                module.onDisable();
                moduleStates.put(moduleName, ModuleState.DISABLED);
                corePlugin.getLogger().info("Module disabled: {}", module.getName());
            } catch (Exception e) {
                corePlugin.getLogger().error("Error disabling module: {}", module.getName(), e);
                moduleStates.put(moduleName, ModuleState.ERROR);
            }
        }
        
        corePlugin.getLogger().info("All enabled modules have been processed for disabling.");
        modules.clear();
        moduleStates.clear();
    }

    /**
     * Gets a module by name.
     * @param moduleName The name of the module.
     * @return An Optional containing the module if found, empty otherwise.
     */
    public Optional<IModule> getModule(String moduleName) {
        return Optional.ofNullable(modules.get(moduleName.toLowerCase()));
    }

    /**
     * Gets the state of a module.
     * @param moduleName The name of the module.
     * @return The module state, or null if module not found.
     */
    public ModuleState getModuleState(String moduleName) {
        return moduleStates.get(moduleName.toLowerCase());
    }

    /**
     * Checks if a module is registered.
     * @param moduleName The name of the module.
     * @return true if the module is registered, false otherwise.
     */
    public boolean isModuleRegistered(String moduleName) {
        return modules.containsKey(moduleName.toLowerCase());
    }

    /**
     * Checks if a module is enabled.
     * @param moduleName The name of the module.
     * @return true if the module is enabled, false otherwise.
     */
    public boolean isModuleEnabled(String moduleName) {
        return moduleStates.get(moduleName.toLowerCase()) == ModuleState.ENABLED;
    }

    /**
     * Gets all registered module names.
     * @return A set of module names.
     */
    public Set<String> getRegisteredModuleNames() {
        return modules.keySet();
    }

    /**
     * Gets the count of modules in each state.
     * @return A map of state to count.
     */
    public Map<ModuleState, Integer> getModuleStateCounts() {
        Map<ModuleState, Integer> counts = new LinkedHashMap<>();
        for (ModuleState state : ModuleState.values()) {
            counts.put(state, 0);
        }
        
        for (ModuleState state : moduleStates.values()) {
            counts.put(state, counts.get(state) + 1);
        }
        
        return counts;
    }

    /**
     * Reloads a specific module.
     * @param moduleName The name of the module to reload.
     * @return true if the module was reloaded successfully, false otherwise.
     */
    public boolean reloadModule(String moduleName) {
        Optional<IModule> moduleOpt = getModule(moduleName);
        if (moduleOpt.isEmpty()) {
            corePlugin.getLogger().warn("Cannot reload module '{}' - module not found.", moduleName);
            return false;
        }
        
        IModule module = moduleOpt.get();
        String moduleKey = moduleName.toLowerCase();
        
        try {
            corePlugin.getLogger().info("Reloading module: {}", module.getName());
            
            // Disable the module if it's enabled
            if (moduleStates.get(moduleKey) == ModuleState.ENABLED) {
                module.onDisable();
            }
            
            // Re-load the module
            module.onLoad(corePlugin);
            moduleStates.put(moduleKey, ModuleState.LOADED);
            
            // Re-enable the module
            module.onEnable(corePlugin);
            moduleStates.put(moduleKey, ModuleState.ENABLED);
            
            corePlugin.getLogger().info("Module reloaded successfully: {}", module.getName());
            return true;
        } catch (Exception e) {
            corePlugin.getLogger().error("Error reloading module: {}", module.getName(), e);
            moduleStates.put(moduleKey, ModuleState.ERROR);
            return false;
        }
    }

    /**
     * Reloads all registered modules.
     * @return The number of successfully reloaded modules.
     */
    public int reloadAllModules() {
        corePlugin.getLogger().info("Reloading all registered modules...");
        int successCount = 0;
        
        for (String moduleName : modules.keySet()) {
            if (reloadModule(moduleName)) {
                successCount++;
            }
        }
        
        corePlugin.getLogger().info("Reloaded {}/{} modules successfully.", successCount, modules.size());
        return successCount;
    }

    /**
     * Gets a formatted status report of all modules.
     * @return A formatted string containing module status information.
     */
    public String getModuleStatusReport() {
        StringBuilder report = new StringBuilder();
        report.append("Module Status Report:\n");
        report.append("====================\n");
        
        Map<ModuleState, Integer> counts = getModuleStateCounts();
        report.append(String.format("Total Modules: %d\n", modules.size()));
        for (Map.Entry<ModuleState, Integer> entry : counts.entrySet()) {
            if (entry.getValue() > 0) {
                report.append(String.format("%s: %d\n", entry.getKey(), entry.getValue()));
            }
        }
        
        report.append("\nModule Details:\n");
        for (Map.Entry<String, IModule> entry : modules.entrySet()) {
            String moduleName = entry.getKey();
            IModule module = entry.getValue();
            ModuleState state = moduleStates.get(moduleName);
            
            report.append(String.format("- %s v%s [%s]: %s\n", 
                module.getName(), 
                module.getVersion(), 
                state, 
                module.getDescription()));
        }
        
        return report.toString();
    }

    /**
     * Enum representing the different states a module can be in.
     */
    public enum ModuleState {
        REGISTERED("Registered"),
        LOADED("Loaded"),
        ENABLED("Enabled"),
        DISABLED("Disabled"),
        ERROR("Error");

        private final String displayName;

        ModuleState(String displayName) {
            this.displayName = displayName;
        }

        @Override
        public String toString() {
            return displayName;
        }
    }
}
