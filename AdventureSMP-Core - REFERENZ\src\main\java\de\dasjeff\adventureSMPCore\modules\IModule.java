package de.dasjeff.adventureSMPCore.modules;

import de.dasjeff.adventureSMPCore.AdventureSMPCore;

/**
 * Interface for all modules in the AdventureSMPCore plugin.
 */
public interface IModule {

    /**
     * Gets the unique name of the module.
     * @return The name of the module.
     */
    String getName();

    /**
     * Called when the plugin is loading.
     */
    void onLoad(AdventureSMPCore corePlugin);

    /**
     * Called when the module is being enabled.
     * @param corePlugin The main plugin instance.
     */
    void onEnable(AdventureSMPCore corePlugin);

    /**
     * Called when the module is being disabled.
     */
    void onDisable();

    /**
     * Optional: Allows modules to register their default configuration values.
     * @param configManager The global ConfigManager instance.
     * @param moduleConfigKey The key under which this module's configuration is stored or the name of its dedicated config file.
     */
} 