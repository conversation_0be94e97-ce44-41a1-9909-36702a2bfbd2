package de.dasjeff.aSMPVCore.managers;

import com.velocitypowered.api.command.Command;
import com.velocitypowered.api.command.CommandMeta;
import de.dasjeff.aSMPVCore.ASMPVCore;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * Manages command registration and unregistration for the ASMP-VCore plugin.
 * Provides a centralized way to handle Velocity commands.
 */
public class CommandManager {

    private final ASMPVCore corePlugin;
    private final com.velocitypowered.api.command.CommandManager velocityCommandManager;
    private final Map<String, CommandRegistration> registeredCommands = new HashMap<>();

    public CommandManager(ASMPVCore corePlugin) {
        this.corePlugin = corePlugin;
        this.velocityCommandManager = corePlugin.getProxyServer().getCommandManager();
    }

    /**
     * Registers a command with the Velocity command manager.
     * @param command The command instance to register.
     * @param primaryAlias The primary alias for the command.
     * @param aliases Additional aliases for the command.
     */
    public void registerCommand(Command command, String primaryAlias, String... aliases) {
        try {
            // Build command meta
            CommandMeta.Builder metaBuilder = velocityCommandManager.metaBuilder(primaryAlias);
            if (aliases.length > 0) {
                metaBuilder.aliases(aliases);
            }
            CommandMeta meta = metaBuilder.build();

            // Register with Velocity
            velocityCommandManager.register(meta, command);

            // Store registration info
            CommandRegistration registration = new CommandRegistration(command, meta, primaryAlias, aliases);
            registeredCommands.put(primaryAlias.toLowerCase(), registration);

            corePlugin.getLogger().info("Registered command '{}' with {} aliases", primaryAlias, aliases.length);
        } catch (Exception e) {
            corePlugin.getLogger().error("Failed to register command '{}'", primaryAlias, e);
        }
    }

    /**
     * Registers a command with only a primary alias.
     * @param command The command instance to register.
     * @param primaryAlias The primary alias for the command.
     */
    public void registerCommand(Command command, String primaryAlias) {
        registerCommand(command, primaryAlias, new String[0]);
    }

    /**
     * Unregisters a command by its primary alias.
     * @param primaryAlias The primary alias of the command to unregister.
     */
    public void unregisterCommand(String primaryAlias) {
        CommandRegistration registration = registeredCommands.get(primaryAlias.toLowerCase());
        if (registration != null) {
            try {
                velocityCommandManager.unregister(registration.getMeta());
                registeredCommands.remove(primaryAlias.toLowerCase());
                corePlugin.getLogger().info("Unregistered command '{}'", primaryAlias);
            } catch (Exception e) {
                corePlugin.getLogger().error("Failed to unregister command '{}'", primaryAlias, e);
            }
        } else {
            corePlugin.getLogger().warn("Attempted to unregister command '{}' but it was not found", primaryAlias);
        }
    }

    /**
     * Unregisters all commands that were registered through this manager.
     */
    public void unregisterAllCommands() {
        corePlugin.getLogger().info("Unregistering all commands...");

        // Create a copy of the keys to avoid ConcurrentModificationException
        Set<String> commandNames = Set.copyOf(registeredCommands.keySet());

        for (String commandName : commandNames) {
            unregisterCommand(commandName);
        }

        corePlugin.getLogger().info("All commands have been unregistered.");
    }

    /**
     * Checks if a command is registered with this manager.
     * @param primaryAlias The primary alias to check.
     * @return true if the command is registered, false otherwise.
     */
    public boolean isCommandRegistered(String primaryAlias) {
        return registeredCommands.containsKey(primaryAlias.toLowerCase());
    }

    /**
     * Gets a registered command by its primary alias.
     * @param primaryAlias The primary alias of the command.
     * @return The command instance, or null if not found.
     */
    public Command getRegisteredCommand(String primaryAlias) {
        CommandRegistration registration = registeredCommands.get(primaryAlias.toLowerCase());
        return registration != null ? registration.getCommand() : null;
    }

    /**
     * Gets all registered command aliases.
     * @return A set of all registered command aliases.
     */
    public Set<String> getRegisteredCommandAliases() {
        return Set.copyOf(registeredCommands.keySet());
    }

    /**
     * Gets the number of registered commands.
     * @return The number of registered commands.
     */
    public int getRegisteredCommandCount() {
        return registeredCommands.size();
    }

    /**
     * Gets a formatted status report of all registered commands.
     * @return A formatted string containing command information.
     */
    public String getCommandStatusReport() {
        StringBuilder report = new StringBuilder();
        report.append("Command Status Report:\n");
        report.append("=====================\n");
        report.append(String.format("Total Registered Commands: %d\n\n", registeredCommands.size()));

        if (registeredCommands.isEmpty()) {
            report.append("No commands are currently registered.\n");
        } else {
            report.append("Registered Commands:\n");
            for (Map.Entry<String, CommandRegistration> entry : registeredCommands.entrySet()) {
                CommandRegistration registration = entry.getValue();
                report.append(String.format("- %s", registration.getPrimaryAlias()));

                if (registration.getAliases().length > 0) {
                    report.append(" (aliases: ");
                    for (int i = 0; i < registration.getAliases().length; i++) {
                        if (i > 0) report.append(", ");
                        report.append(registration.getAliases()[i]);
                    }
                    report.append(")");
                }

                report.append(String.format(" [%s]\n", registration.getCommand().getClass().getSimpleName()));
            }
        }

        return report.toString();
    }

    /**
     * Inner class to store command registration information.
     */
    private static class CommandRegistration {
        private final Command command;
        private final CommandMeta meta;
        private final String primaryAlias;
        private final String[] aliases;

        public CommandRegistration(Command command, CommandMeta meta, String primaryAlias, String[] aliases) {
            this.command = command;
            this.meta = meta;
            this.primaryAlias = primaryAlias;
            this.aliases = aliases.clone(); // Defensive copy
        }

        public Command getCommand() {
            return command;
        }

        public CommandMeta getMeta() {
            return meta;
        }

        public String getPrimaryAlias() {
            return primaryAlias;
        }

        public String[] getAliases() {
            return aliases.clone(); // Defensive copy
        }
    }
}
