package de.dasjeff.aSMPVCore.util;

import java.util.UUID;
import java.util.regex.Pattern;

/**
 * Utility class for UUID operations and validation.
 * Provides methods for UUID manipulation and validation.
 */
public final class UUIDUtil {

    private static final Pattern UUID_PATTERN = Pattern.compile(
        "^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$"
    );
    
    private static final Pattern UUID_PATTERN_NO_DASHES = Pattern.compile(
        "^[0-9a-fA-F]{32}$"
    );

    private UUIDUtil() {
        // Utility class - no instantiation
    }

    /**
     * Validates if a string is a valid UUID format.
     * @param uuidString The string to validate.
     * @return true if valid UUID format, false otherwise.
     */
    public static boolean isValidUUID(String uuidString) {
        if (uuidString == null || uuidString.isEmpty()) {
            return false;
        }
        
        return UUID_PATTERN.matcher(uuidString).matches();
    }

    /**
     * Validates if a string is a valid UUID format without dashes.
     * @param uuidString The string to validate.
     * @return true if valid UUID format without dashes, false otherwise.
     */
    public static boolean isValidUUIDNoDashes(String uuidString) {
        if (uuidString == null || uuidString.isEmpty()) {
            return false;
        }
        
        return UUID_PATTERN_NO_DASHES.matcher(uuidString).matches();
    }

    /**
     * Safely parses a UUID string.
     * @param uuidString The string to parse.
     * @return The UUID if valid, null otherwise.
     */
    public static UUID parseUUID(String uuidString) {
        if (!isValidUUID(uuidString)) {
            return null;
        }
        
        try {
            return UUID.fromString(uuidString);
        } catch (IllegalArgumentException e) {
            return null;
        }
    }

    /**
     * Safely parses a UUID string without dashes.
     * @param uuidString The string to parse (without dashes).
     * @return The UUID if valid, null otherwise.
     */
    public static UUID parseUUIDNoDashes(String uuidString) {
        if (!isValidUUIDNoDashes(uuidString)) {
            return null;
        }
        
        try {
            // Add dashes to the UUID string
            String formattedUuid = uuidString.substring(0, 8) + "-" +
                                 uuidString.substring(8, 12) + "-" +
                                 uuidString.substring(12, 16) + "-" +
                                 uuidString.substring(16, 20) + "-" +
                                 uuidString.substring(20, 32);
            
            return UUID.fromString(formattedUuid);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * Converts a UUID to a string without dashes.
     * @param uuid The UUID to convert.
     * @return The UUID string without dashes.
     */
    public static String toStringNoDashes(UUID uuid) {
        if (uuid == null) {
            return null;
        }
        
        return uuid.toString().replace("-", "");
    }

    /**
     * Adds dashes to a UUID string that doesn't have them.
     * @param uuidNoDashes The UUID string without dashes.
     * @return The UUID string with dashes, or null if invalid.
     */
    public static String addDashes(String uuidNoDashes) {
        if (!isValidUUIDNoDashes(uuidNoDashes)) {
            return null;
        }
        
        return uuidNoDashes.substring(0, 8) + "-" +
               uuidNoDashes.substring(8, 12) + "-" +
               uuidNoDashes.substring(12, 16) + "-" +
               uuidNoDashes.substring(16, 20) + "-" +
               uuidNoDashes.substring(20, 32);
    }

    /**
     * Removes dashes from a UUID string.
     * @param uuidWithDashes The UUID string with dashes.
     * @return The UUID string without dashes, or null if invalid.
     */
    public static String removeDashes(String uuidWithDashes) {
        if (!isValidUUID(uuidWithDashes)) {
            return null;
        }
        
        return uuidWithDashes.replace("-", "");
    }

    /**
     * Normalizes a UUID string to the standard format with dashes.
     * @param uuidString The UUID string (with or without dashes).
     * @return The normalized UUID string with dashes, or null if invalid.
     */
    public static String normalize(String uuidString) {
        if (uuidString == null || uuidString.isEmpty()) {
            return null;
        }
        
        // Remove any existing dashes
        String noDashes = uuidString.replace("-", "");
        
        // Check if it's a valid UUID without dashes
        if (isValidUUIDNoDashes(noDashes)) {
            return addDashes(noDashes);
        }
        
        return null;
    }

    /**
     * Compares two UUID strings for equality, handling different formats.
     * @param uuid1 The first UUID string.
     * @param uuid2 The second UUID string.
     * @return true if the UUIDs are equal, false otherwise.
     */
    public static boolean equals(String uuid1, String uuid2) {
        String normalized1 = normalize(uuid1);
        String normalized2 = normalize(uuid2);
        
        if (normalized1 == null || normalized2 == null) {
            return false;
        }
        
        return normalized1.equals(normalized2);
    }

    /**
     * Generates a random UUID.
     * @return A new random UUID.
     */
    public static UUID randomUUID() {
        return UUID.randomUUID();
    }

    /**
     * Generates a random UUID as a string.
     * @return A new random UUID as a string.
     */
    public static String randomUUIDString() {
        return UUID.randomUUID().toString();
    }

    /**
     * Generates a random UUID as a string without dashes.
     * @return A new random UUID as a string without dashes.
     */
    public static String randomUUIDStringNoDashes() {
        return toStringNoDashes(UUID.randomUUID());
    }

    /**
     * Checks if a UUID is the nil UUID (all zeros).
     * @param uuid The UUID to check.
     * @return true if the UUID is nil, false otherwise.
     */
    public static boolean isNilUUID(UUID uuid) {
        if (uuid == null) {
            return false;
        }
        
        return uuid.equals(new UUID(0L, 0L));
    }

    /**
     * Checks if a UUID string is the nil UUID (all zeros).
     * @param uuidString The UUID string to check.
     * @return true if the UUID is nil, false otherwise.
     */
    public static boolean isNilUUID(String uuidString) {
        UUID uuid = parseUUID(normalize(uuidString));
        return uuid != null && isNilUUID(uuid);
    }

    /**
     * Gets the nil UUID (all zeros).
     * @return The nil UUID.
     */
    public static UUID getNilUUID() {
        return new UUID(0L, 0L);
    }

    /**
     * Gets the nil UUID as a string.
     * @return The nil UUID as a string.
     */
    public static String getNilUUIDString() {
        return getNilUUID().toString();
    }

    /**
     * Validates and converts a UUID string to a UUID object.
     * Handles both formats (with and without dashes).
     * @param uuidString The UUID string to convert.
     * @return The UUID object, or null if invalid.
     */
    public static UUID fromString(String uuidString) {
        if (uuidString == null || uuidString.isEmpty()) {
            return null;
        }
        
        // Try with dashes first
        if (isValidUUID(uuidString)) {
            return parseUUID(uuidString);
        }
        
        // Try without dashes
        if (isValidUUIDNoDashes(uuidString)) {
            return parseUUIDNoDashes(uuidString);
        }
        
        return null;
    }
}
