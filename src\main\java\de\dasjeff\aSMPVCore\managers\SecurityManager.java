package de.dasjeff.aSMPVCore.managers;

import de.dasjeff.aSMPVCore.ASMPVCore;
import de.dasjeff.aSMPVCore.managers.ConfigManager.ConfigWrapper;

import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Security manager for rate limiting, DoS protection, and security validation.
 * Provides protection against abuse and ensures secure operations.
 */
public class SecurityManager {

    private final ASMPVCore corePlugin;
    private final ConcurrentHashMap<UUID, AtomicLong> lastCommandTime = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<UUID, AtomicInteger> asyncOperationCount = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, AtomicInteger> rateLimitCounters = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, AtomicLong> rateLimitResetTime = new ConcurrentHashMap<>();
    
    // Configuration
    private final long commandCooldownMs;
    private final int maxAsyncOperationsPerPlayer;
    private final boolean rateLimitEnabled;
    private final int rateLimitRequestsPerMinute;
    
    // Statistics
    private final AtomicLong totalCommandsBlocked = new AtomicLong(0);
    private final AtomicLong totalAsyncOperationsBlocked = new AtomicLong(0);
    private final AtomicLong totalRateLimitViolations = new AtomicLong(0);

    public SecurityManager(ASMPVCore corePlugin) {
        this.corePlugin = corePlugin;
        
        ConfigWrapper config = corePlugin.getConfigManager().getMainConfig();
        this.commandCooldownMs = config.getLong("security.command_cooldown_ms", 100);
        this.maxAsyncOperationsPerPlayer = config.getInt("security.max_async_operations_per_player", 10);
        this.rateLimitEnabled = config.getBoolean("security.rate_limit_enabled", true);
        this.rateLimitRequestsPerMinute = config.getInt("security.rate_limit_requests_per_minute", 60);
    }

    /**
     * Checks if a player can execute a command based on cooldown.
     * @param playerUuid The player's UUID.
     * @return true if the command can be executed, false if on cooldown.
     */
    public boolean canExecuteCommand(UUID playerUuid) {
        if (commandCooldownMs <= 0) {
            return true;
        }
        
        long currentTime = System.currentTimeMillis();
        AtomicLong lastTime = lastCommandTime.computeIfAbsent(playerUuid, k -> new AtomicLong(0));
        
        long timeSinceLastCommand = currentTime - lastTime.get();
        if (timeSinceLastCommand >= commandCooldownMs) {
            lastTime.set(currentTime);
            return true;
        } else {
            totalCommandsBlocked.incrementAndGet();
            return false;
        }
    }

    /**
     * Checks if a player can start an async operation.
     * @param playerUuid The player's UUID.
     * @return true if the operation can be started, false if limit exceeded.
     */
    public boolean canStartAsyncOperation(UUID playerUuid) {
        AtomicInteger count = asyncOperationCount.computeIfAbsent(playerUuid, k -> new AtomicInteger(0));
        
        if (count.get() >= maxAsyncOperationsPerPlayer) {
            totalAsyncOperationsBlocked.incrementAndGet();
            return false;
        }
        
        count.incrementAndGet();
        return true;
    }

    /**
     * Marks an async operation as completed for a player.
     * @param playerUuid The player's UUID.
     */
    public void completeAsyncOperation(UUID playerUuid) {
        AtomicInteger count = asyncOperationCount.get(playerUuid);
        if (count != null) {
            count.decrementAndGet();
            // Clean up if count reaches 0
            if (count.get() <= 0) {
                asyncOperationCount.remove(playerUuid);
            }
        }
    }

    /**
     * Checks rate limiting for a specific identifier (IP, player, etc.).
     * @param identifier The identifier to check (e.g., IP address, player UUID).
     * @return true if within rate limit, false if limit exceeded.
     */
    public boolean checkRateLimit(String identifier) {
        if (!rateLimitEnabled) {
            return true;
        }
        
        long currentTime = System.currentTimeMillis();
        long currentMinute = currentTime / 60000; // Convert to minute buckets
        
        String key = identifier + ":" + currentMinute;
        AtomicInteger counter = rateLimitCounters.computeIfAbsent(key, k -> new AtomicInteger(0));
        AtomicLong resetTime = rateLimitResetTime.computeIfAbsent(key, k -> new AtomicLong(currentTime + 60000));
        
        // Clean up old entries
        if (currentTime > resetTime.get()) {
            rateLimitCounters.remove(key);
            rateLimitResetTime.remove(key);
            counter = new AtomicInteger(0);
            rateLimitCounters.put(key, counter);
            rateLimitResetTime.put(key, new AtomicLong(currentTime + 60000));
        }
        
        int currentCount = counter.incrementAndGet();
        if (currentCount > rateLimitRequestsPerMinute) {
            totalRateLimitViolations.incrementAndGet();
            return false;
        }
        
        return true;
    }

    /**
     * Validates if a string is safe (no SQL injection, XSS, etc.).
     * @param input The input string to validate.
     * @return true if safe, false if potentially dangerous.
     */
    public boolean isInputSafe(String input) {
        if (input == null || input.isEmpty()) {
            return true;
        }
        
        // Check for common SQL injection patterns
        String lowerInput = input.toLowerCase();
        String[] sqlKeywords = {
            "select", "insert", "update", "delete", "drop", "create", "alter",
            "union", "exec", "execute", "script", "javascript", "vbscript",
            "onload", "onerror", "onclick", "<script", "</script>", "eval("
        };
        
        for (String keyword : sqlKeywords) {
            if (lowerInput.contains(keyword)) {
                return false;
            }
        }
        
        // Check for suspicious characters
        char[] suspiciousChars = {'\'', '"', ';', '<', '>', '&', '%'};
        for (char c : suspiciousChars) {
            if (input.indexOf(c) != -1) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * Validates a player name format.
     * @param playerName The player name to validate.
     * @return true if valid, false otherwise.
     */
    public boolean isValidPlayerName(String playerName) {
        if (playerName == null || playerName.isEmpty()) {
            return false;
        }
        
        // Minecraft player name rules: 3-16 characters, alphanumeric and underscore only
        return playerName.length() >= 3 && playerName.length() <= 16 
               && playerName.matches("^[a-zA-Z0-9_]+$");
    }

    /**
     * Validates a UUID format.
     * @param uuid The UUID to validate.
     * @return true if valid, false otherwise.
     */
    public boolean isValidUUID(UUID uuid) {
        return uuid != null;
    }

    /**
     * Validates a UUID string format.
     * @param uuidString The UUID string to validate.
     * @return true if valid, false otherwise.
     */
    public boolean isValidUUIDString(String uuidString) {
        if (uuidString == null || uuidString.isEmpty()) {
            return false;
        }
        
        try {
            UUID.fromString(uuidString);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }

    /**
     * Cleans up expired entries from security maps.
     * Should be called periodically to prevent memory leaks.
     */
    public void cleanupExpiredEntries() {
        long currentTime = System.currentTimeMillis();
        
        // Clean up rate limit entries older than 2 minutes
        rateLimitResetTime.entrySet().removeIf(entry -> {
            if (currentTime > entry.getValue().get() + 60000) {
                rateLimitCounters.remove(entry.getKey());
                return true;
            }
            return false;
        });
        
        // Clean up command cooldowns older than 1 minute
        lastCommandTime.entrySet().removeIf(entry -> 
            currentTime - entry.getValue().get() > 60000);
        
        // Clean up async operation counts for inactive players
        asyncOperationCount.entrySet().removeIf(entry -> entry.getValue().get() <= 0);
    }

    /**
     * Gets security statistics as a formatted string.
     * @return Security statistics.
     */
    public String getSecurityStats() {
        return String.format(
            "Security Stats - Commands Blocked: %d, Async Ops Blocked: %d, Rate Limit Violations: %d, " +
            "Active Players: %d, Rate Limit Entries: %d",
            totalCommandsBlocked.get(),
            totalAsyncOperationsBlocked.get(),
            totalRateLimitViolations.get(),
            lastCommandTime.size(),
            rateLimitCounters.size()
        );
    }

    /**
     * Resets all security counters and maps.
     * Useful for testing or administrative purposes.
     */
    public void resetSecurityCounters() {
        lastCommandTime.clear();
        asyncOperationCount.clear();
        rateLimitCounters.clear();
        rateLimitResetTime.clear();
        totalCommandsBlocked.set(0);
        totalAsyncOperationsBlocked.set(0);
        totalRateLimitViolations.set(0);
        
        corePlugin.getLogger().info("Security counters have been reset");
    }

    /**
     * Gets the current async operation count for a player.
     * @param playerUuid The player's UUID.
     * @return The current async operation count.
     */
    public int getAsyncOperationCount(UUID playerUuid) {
        AtomicInteger count = asyncOperationCount.get(playerUuid);
        return count != null ? count.get() : 0;
    }

    /**
     * Gets the remaining cooldown time for a player's commands.
     * @param playerUuid The player's UUID.
     * @return The remaining cooldown time in milliseconds, or 0 if no cooldown.
     */
    public long getRemainingCooldown(UUID playerUuid) {
        if (commandCooldownMs <= 0) {
            return 0;
        }
        
        AtomicLong lastTime = lastCommandTime.get(playerUuid);
        if (lastTime == null) {
            return 0;
        }
        
        long timeSinceLastCommand = System.currentTimeMillis() - lastTime.get();
        return Math.max(0, commandCooldownMs - timeSinceLastCommand);
    }
}
