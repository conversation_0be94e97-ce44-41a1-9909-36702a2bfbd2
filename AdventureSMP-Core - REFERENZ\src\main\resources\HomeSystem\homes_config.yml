# ===================================================================
#              Konfiguration für das AdventureSMP HomeSystem
# ===================================================================


# ===================================================================
#                               LIMITS
# ===================================================================
limits:
  # Legt die standardmäßige maximale <PERSON>zahl an Home-Punkten fest,
  # die ein Spieler setzen kann.
  default_max_homes: 3

  # Definiert den Präfix für die Berechtigungs-Nodes, die ein spezifisches
  # Limit für Home-Punkte festlegen.
  # Beispiel: "adventuresmp.core.homes.limit.5" erlaubt dem Spieler 5 Home-Punkte.
  permission_prefix: "adventuresmp.core.homes.limit."

  # Der Berechtigungs-Node für eine unbegrenzte Anzahl an Home-Punkten.
  unlimited_node: "adventuresmp.core.homes.limit.unlimited"


# ===================================================================
#                          TELEPORTATION
# ===================================================================
teleport:
  # Verzögerung in Sekunden, bevor ein Spieler nach der Eingabe von /home
  # teleportiert wird.
  delay_seconds: 3

  # Wenn 'true', muss der Spieler während der Verzögerung (delay_seconds)
  # stillstehen. Bewegt er sich, wird die Teleportation abgebrochen.
  cancel_on_move: true

  # Abklingzeit in Sekunden, bevor ein Spieler den /home Befehl erneut
  # verwenden kann. Auf 0 setzen, um die Abklingzeit zu deaktivieren.
  cooldown_seconds: 10


# ===================================================================
#                      EINSCHRÄNKUNGEN
# ===================================================================
restrictions:
  # Liste von Welt-Namen, in denen Spieler kein Zuhause setzen dürfen.
  # Administratoren können diese Einschränkung mit Admin-Befehlen umgehen.
  #
  # Beispiel:
  # blacklisted_worlds:
  #   - "farmwelt"
  #   - "event"
  blacklisted_worlds: []


# ===================================================================
#                          HOME-NAMEN
# ===================================================================
home_names:
  # Minimale erforderliche Länge für einen Home-Namen.
  min_length: 3

  # Maximale erlaubte Länge für einen Home-Namen.
  max_length: 25


# ===================================================================
#                             SOUNDS
# ===================================================================
sounds:
  # Um einen Sound zu deaktivieren, setze den Wert auf "".

  # Wird abgespielt, wenn ein Home erfolgreich gesetzt wird.
  set_home: "ENTITY_PLAYER_LEVELUP"

  # Wird abgespielt, wenn ein Home gelöscht wird.
  delete_home: "ENTITY_EXPERIENCE_ORB_PICKUP"

  # Wird zu Beginn der Teleportation abgespielt (nur wenn 'delay_seconds' > 0 ist).
  teleport_initiate: "BLOCK_NOTE_BLOCK_PLING"

  # Wird nach einer erfolgreichen Teleportation abgespielt.
  teleport_success: "ENTITY_ENDERMAN_TELEPORT"

  # Wird abgespielt, wenn eine Teleportation abgebrochen wird.
  teleport_cancelled: "ENTITY_VILLAGER_NO"

  # Wird bei einem Fehler abgespielt.
  error: "BLOCK_NOTE_BLOCK_BASS"

  # Sound für einen Klick in GUI (/homes).
  gui_click: "UI_BUTTON_CLICK"