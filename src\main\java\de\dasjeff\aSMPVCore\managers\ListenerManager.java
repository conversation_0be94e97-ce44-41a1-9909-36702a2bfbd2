package de.dasjeff.aSMPVCore.managers;

import com.velocitypowered.api.event.EventManager;
import de.dasjeff.aSMPVCore.ASMPVCore;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * Manages event listener registration and unregistration for the ASMP-VCore plugin.
 * Provides a centralized way to handle Velocity event listeners.
 */
public class ListenerManager {

    private final ASMPVCore corePlugin;
    private final EventManager eventManager;
    private final List<ListenerRegistration> registeredListeners = new CopyOnWriteArrayList<>();

    public ListenerManager(ASMPVCore corePlugin) {
        this.corePlugin = corePlugin;
        this.eventManager = corePlugin.getProxyServer().getEventManager();
    }

    /**
     * Registers an event listener with the Velocity event manager.
     * @param listener The listener instance to register.
     */
    public void registerListener(Object listener) {
        try {
            eventManager.register(corePlugin, listener);
            
            ListenerRegistration registration = new ListenerRegistration(listener, listener.getClass().getSimpleName());
            registeredListeners.add(registration);
            
            corePlugin.getLogger().info("Registered listener: {}", listener.getClass().getSimpleName());
        } catch (Exception e) {
            corePlugin.getLogger().error("Failed to register listener: {}", listener.getClass().getSimpleName(), e);
        }
    }

    /**
     * Registers multiple event listeners at once.
     * @param listeners The listener instances to register.
     */
    public void registerListeners(Object... listeners) {
        for (Object listener : listeners) {
            registerListener(listener);
        }
    }

    /**
     * Unregisters a specific event listener.
     * @param listener The listener instance to unregister.
     */
    public void unregisterListener(Object listener) {
        try {
            eventManager.unregisterListener(corePlugin, listener);
            
            // Remove from our tracking list
            registeredListeners.removeIf(registration -> registration.getListener() == listener);
            
            corePlugin.getLogger().info("Unregistered listener: {}", listener.getClass().getSimpleName());
        } catch (Exception e) {
            corePlugin.getLogger().error("Failed to unregister listener: {}", listener.getClass().getSimpleName(), e);
        }
    }

    /**
     * Unregisters multiple event listeners at once.
     * @param listeners The listener instances to unregister.
     */
    public void unregisterListeners(Object... listeners) {
        for (Object listener : listeners) {
            unregisterListener(listener);
        }
    }

    /**
     * Unregisters all listeners that were registered through this manager.
     */
    public void unregisterAllListeners() {
        corePlugin.getLogger().info("Unregistering all listeners...");
        
        // Create a copy to avoid concurrent modification
        List<ListenerRegistration> listenersToUnregister = new ArrayList<>(registeredListeners);
        
        for (ListenerRegistration registration : listenersToUnregister) {
            unregisterListener(registration.getListener());
        }
        
        // Clear the list
        registeredListeners.clear();
        
        corePlugin.getLogger().info("All listeners have been unregistered.");
    }

    /**
     * Checks if a listener is registered with this manager.
     * @param listener The listener instance to check.
     * @return true if the listener is registered, false otherwise.
     */
    public boolean isListenerRegistered(Object listener) {
        return registeredListeners.stream()
                .anyMatch(registration -> registration.getListener() == listener);
    }

    /**
     * Gets all registered listeners.
     * @return A list of all registered listener instances.
     */
    public List<Object> getRegisteredListeners() {
        return registeredListeners.stream()
                .map(ListenerRegistration::getListener)
                .toList();
    }

    /**
     * Gets the number of registered listeners.
     * @return The number of registered listeners.
     */
    public int getRegisteredListenerCount() {
        return registeredListeners.size();
    }

    /**
     * Gets a formatted status report of all registered listeners.
     * @return A formatted string containing listener information.
     */
    public String getListenerStatusReport() {
        StringBuilder report = new StringBuilder();
        report.append("Listener Status Report:\n");
        report.append("======================\n");
        report.append(String.format("Total Registered Listeners: %d\n\n", registeredListeners.size()));
        
        if (registeredListeners.isEmpty()) {
            report.append("No listeners are currently registered.\n");
        } else {
            report.append("Registered Listeners:\n");
            for (ListenerRegistration registration : registeredListeners) {
                report.append(String.format("- %s [%s]\n", 
                    registration.getName(), 
                    registration.getListener().getClass().getName()));
            }
        }
        
        return report.toString();
    }

    /**
     * Gets listeners by their class type.
     * @param listenerClass The class type to search for.
     * @param <T> The listener type.
     * @return A list of listeners of the specified type.
     */
    @SuppressWarnings("unchecked")
    public <T> List<T> getListenersByType(Class<T> listenerClass) {
        return registeredListeners.stream()
                .map(ListenerRegistration::getListener)
                .filter(listenerClass::isInstance)
                .map(listener -> (T) listener)
                .toList();
    }

    /**
     * Checks if any listeners of a specific type are registered.
     * @param listenerClass The class type to check for.
     * @return true if at least one listener of the specified type is registered.
     */
    public boolean hasListenerOfType(Class<?> listenerClass) {
        return registeredListeners.stream()
                .anyMatch(registration -> listenerClass.isInstance(registration.getListener()));
    }

    /**
     * Gets the names of all registered listener classes.
     * @return A set of listener class names.
     */
    public Set<String> getRegisteredListenerNames() {
        return registeredListeners.stream()
                .map(ListenerRegistration::getName)
                .collect(java.util.stream.Collectors.toSet());
    }

    /**
     * Reregisters all listeners (useful for reloading).
     * This unregisters and then re-registers all listeners.
     */
    public void reregisterAllListeners() {
        corePlugin.getLogger().info("Re-registering all listeners...");
        
        // Store current listeners
        List<Object> currentListeners = getRegisteredListeners();
        
        // Unregister all
        unregisterAllListeners();
        
        // Re-register all
        for (Object listener : currentListeners) {
            registerListener(listener);
        }
        
        corePlugin.getLogger().info("Re-registered {} listeners", currentListeners.size());
    }

    /**
     * Inner class to store listener registration information.
     */
    private static class ListenerRegistration {
        private final Object listener;
        private final String name;

        public ListenerRegistration(Object listener, String name) {
            this.listener = listener;
            this.name = name;
        }

        public Object getListener() {
            return listener;
        }

        public String getName() {
            return name;
        }
    }
}
