package de.dasjeff.adventureSMPCore.managers;

import de.dasjeff.adventureSMPCore.AdventureSMPCore;
import de.dasjeff.adventureSMPCore.modules.IModule;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Optional;
import java.util.logging.Level;

public class ModuleManager {

    private final AdventureSMPCore corePlugin;
    private final Map<String, IModule> modules = new LinkedHashMap<>();

    public ModuleManager(AdventureSMPCore corePlugin) {
        this.corePlugin = corePlugin;
    }

    /**
     * Registers a module with the ModuleManager.
     * @param module The module instance to register.
     */
    public void registerModule(IModule module) {
        if (modules.containsKey(module.getName().toLowerCase())) {
            corePlugin.getLogger().warning("Module with name '" + module.getName() + "' is already registered. Skipping.");
            return;
        }
        modules.put(module.getName().toLowerCase(), module);
        corePlugin.getLogger().info("Registered module: " + module.getName());
    }

    /**
     * Loads all registered modules.
     * Calls the onLoad() method of each module.
     */
    public void loadModules() {
        corePlugin.getLogger().info("Loading all registered modules...");
        for (IModule module : modules.values()) {
            try {
                module.onLoad(corePlugin);
                corePlugin.getLogger().info("Module loaded: " + module.getName());
            } catch (Exception e) {
                corePlugin.getLogger().log(Level.SEVERE, "Error loading module: " + module.getName(), e);
                // Optionally, unregister or mark the module as failed to load
            }
        }
        corePlugin.getLogger().info("All registered modules have been processed for loading.");
    }

    /**
     * Enables all registered modules.
     * Calls the onEnable() method of each module.
     */
    public void enableModules() {
        corePlugin.getLogger().info("Enabling all registered modules...");
        for (IModule module : modules.values()) {
            try {
                corePlugin.getLogger().info("Enabling module: " + module.getName() + "...");
                module.onEnable(corePlugin);
                corePlugin.getLogger().info("Module enabled: " + module.getName());
            } catch (Exception e) {
                corePlugin.getLogger().log(Level.SEVERE, "Error enabling module: " + module.getName(), e);
            }
        }
        corePlugin.getLogger().info("All registered modules have been processed for enabling.");
    }

    /**
     * Disables all registered modules.
     * Calls the onDisable() method of each module.
     */
    public void disableModules() {
        corePlugin.getLogger().info("Disabling all registered modules...");
        IModule[] modulesArray = modules.values().toArray(new IModule[0]);
        for (int i = modulesArray.length - 1; i >= 0; i--) {
            IModule module = modulesArray[i];
            try {
                corePlugin.getLogger().info("Disabling module: " + module.getName() + "...");
                module.onDisable();
                corePlugin.getLogger().info("Module disabled: " + module.getName());
            } catch (Exception e) {
                corePlugin.getLogger().log(Level.SEVERE, "Error disabling module: " + module.getName(), e);
            }
        }
        corePlugin.getLogger().info("All registered modules have been processed for disabling.");
        modules.clear();
    }

    /**
     * Retrieves a module by its name.
     * @param name The name of the module.
     * @return An Optional containing the module if found, otherwise an empty Optional.
     */
    public Optional<IModule> getModule(String name) {
        return Optional.ofNullable(modules.get(name.toLowerCase()));
    }

    /**
     * Retrieves a module by its class.
     * @param moduleClass The class of the module.
     * @return An Optional containing the module if found, otherwise an empty Optional.
     */
    public <T extends IModule> Optional<T> getModule(Class<T> moduleClass) {
        return modules.values().stream()
                .filter(moduleClass::isInstance)
                .map(moduleClass::cast)
                .findFirst();
    }

    /**
     * Gets a map of all registered modules.
     * @return A new map containing all registered modules (name -> module instance).
     */
    public Map<String, IModule> getAllModules() {
        return new LinkedHashMap<>(modules); // Return a copy
    }

    /**
     * Reloads a specific module.
     * @param moduleName The name of the module to reload.
     * @return True if the module was reloaded successfully, false otherwise.
     */
    public boolean reloadModule(String moduleName) {
        if (moduleName == null || moduleName.isEmpty()) {
            corePlugin.getLogger().warning("Cannot reload module: No module name provided.");
            return false;
        }
        
        Optional<IModule> moduleOpt = getModule(moduleName);
        if (moduleOpt.isEmpty()) {
            corePlugin.getLogger().warning("Cannot reload module: Module '" + moduleName + "' not found.");
            return false;
        }
        
        IModule module = moduleOpt.get();
        try {
            corePlugin.getLogger().info("Reloading module: " + module.getName());
            
            // Disable the module
            module.onDisable();
            
            // Re-load the module
            module.onLoad(corePlugin);
            
            // Re-enable the module
            module.onEnable(corePlugin);
            
            corePlugin.getLogger().info("Module reloaded successfully: " + module.getName());
            return true;
        } catch (Exception e) {
            corePlugin.getLogger().log(Level.SEVERE, "Error reloading module: " + module.getName(), e);
            return false;
        }
    }

    /**
     * Reloads all registered modules.
     * @return The number of successfully reloaded modules.
     */
    public int reloadAllModules() {
        corePlugin.getLogger().info("Reloading all registered modules...");
        int successCount = 0;
        
        for (IModule module : modules.values()) {
            try {
                corePlugin.getLogger().info("Reloading module: " + module.getName());
                
                // Disable the module
                module.onDisable();
                
                // Re-load the module
                module.onLoad(corePlugin);
                
                // Re-enable the module
                module.onEnable(corePlugin);
                
                corePlugin.getLogger().info("Module reloaded successfully: " + module.getName());
                successCount++;
            } catch (Exception e) {
                corePlugin.getLogger().log(Level.SEVERE, "Error reloading module: " + module.getName(), e);
            }
        }
        
        corePlugin.getLogger().info("Completed reloading modules. Success: " + successCount + "/" + modules.size());
        return successCount;
    }
}