package de.dasjeff.adventureSMPCore.modules.homesystem.validation;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/**
 * Represents the result of a validation operation.
 */
public class ValidationResult {
    
    private final boolean success;
    private final String errorMessageKey;
    private final String[] replacements;

    private ValidationResult(boolean success, String errorMessageKey, String[] replacements) {
        this.success = success;
        this.errorMessageKey = errorMessageKey;
        this.replacements = replacements;
    }

    /**
     * Creates a successful validation result.
     */
    public static ValidationResult success() {
        return new ValidationResult(true, null, null);
    }

    /**
     * Creates a failed validation result with an error message key.
     */
    public static ValidationResult error(@NotNull String errorMessageKey, String... replacements) {
        return new ValidationResult(false, errorMessageKey, replacements);
    }

    /**
     * Returns whether the validation was successful.
     */
    public boolean isSuccess() {
        return success;
    }

    /**
     * Returns the error message key if validation failed.
     */
    @Nullable
    public String getErrorMessageKey() {
        return errorMessageKey;
    }

    /**
     * Returns the replacements for the error message.
     */
    @Nullable
    public String[] getReplacements() {
        return replacements;
    }
} 