package de.dasjeff.adventureSMPCore.util;

import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.minimessage.MiniMessage;
import net.kyori.adventure.text.serializer.legacy.LegacyComponentSerializer;
import net.kyori.adventure.text.serializer.plain.PlainTextComponentSerializer;
import net.kyori.adventure.title.Title;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

import java.time.Duration;

public class ChatUtil {

    private static final MiniMessage miniMessage = MiniMessage.builder().build();

    /**
     * Translates legacy color codes (&) into a string with ChatColor-like section codes (§).
     * @param message The string message with ampersand color codes.
     * @return The formatted String with section codes.
     */
    public static String formatLegacy(String message) {
        if (message == null || message.isEmpty()) return "";
        Component component = LegacyComponentSerializer.legacyAmpersand().deserialize(message);
        return LegacyComponentSerializer.legacySection().serialize(component);
    }

    /**
     * Sends a formatted message to a CommandSender.
     * @param sender The recipient of the message.
     * @param message The message string with legacy ampersand color codes.
     */
    public static void sendMessage(CommandSender sender, String message) {
        if (message == null || message.isEmpty()) return;
        sender.sendMessage(convertLegacyToComponent(message));
    }

    /**
     * Sends a formatted message to a Player's action bar.
     * @param player The player to send the action bar message to.
     * @param message The message string with legacy ampersand color codes.
     */
    public static void sendActionBar(Player player, String message) {
        if (message == null || message.isEmpty()) return;
        player.sendActionBar(convertLegacyToComponent(message));
    }

    /**
     * Sends a subtitle message to a Player that appears below the crosshair.
     * @param player The player to send the subtitle to.
     * @param message The message string with legacy ampersand color codes.
     */
    public static void sendSubtitle(Player player, String message) {
        if (message == null || message.isEmpty()) return;
        
        Title title = Title.title(
            Component.empty(),
            convertLegacyToComponent(message),
            Title.Times.times(
                Duration.ofMillis(0),
                Duration.ofMillis(1200),
                Duration.ofMillis(200)
            )
        );
        
        player.showTitle(title);
    }

    /**
     * Strips all legacy ampersand (&) color codes from a string.
     * @param message The message to strip.
     * @return The plain text message.
     */
    public static String stripColor(String message) {
        if (message == null || message.isEmpty()) return "";
        Component component = convertLegacyToComponent(message);
        return PlainTextComponentSerializer.plainText().serialize(component);
    }
    
    /**
     * Converts a legacy formatted string (&a, &b, etc.) to a Component.
     * @param legacyText Text with legacy formatting codes
     * @return A properly formatted Component
     */
    public static Component convertLegacyToComponent(String legacyText) {
        if (legacyText == null || legacyText.isEmpty()) return Component.empty();
        String sectionFormatted = legacyText.replace('&', '§');
        return LegacyComponentSerializer.legacySection().deserialize(sectionFormatted);
    }
    
    /**
     * Converts a legacy formatted string to MiniMessage format.
     * @param legacyText Text with legacy formatting codes
     * @return Text in MiniMessage format
     */
    public static String convertLegacyToMiniMessage(String legacyText) {
        if (legacyText == null || legacyText.isEmpty()) return "";
        Component component = convertLegacyToComponent(legacyText);
        return miniMessage.serialize(component);
    }
} 